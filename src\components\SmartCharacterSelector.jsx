import React, { useState, useEffect } from 'react';
import { 
  User, 
  Crown, 
  Heart, 
  Zap, 
  Shield, 
  Sparkles,
  ChevronDown,
  Users
} from 'lucide-react';

const SmartCharacterSelector = ({
  characters,
  activeCharacter,
  onCharacterSelect,
  storyArc,
  chatHistory,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [characterSuggestions, setCharacterSuggestions] = useState([]);

  // Generate character suggestions based on context
  useEffect(() => {
    if (characters && storyArc && chatHistory) {
      const suggestions = generateCharacterSuggestions(characters, storyArc, chatHistory);
      setCharacterSuggestions(suggestions);
    }
  }, [characters, storyArc, chatHistory]);

  const getCharacterIcon = (character) => {
    const type = character.type?.toLowerCase() || 'modern';
    const mood = character.mood?.toLowerCase() || 'neutral';
    
    // Type-based icons
    const typeIcons = {
      fantasy: <Sparkles className="h-4 w-4" />,
      superhero: <Shield className="h-4 w-4" />,
      royal: <Crown className="h-4 w-4" />,
      romantic: <Heart className="h-4 w-4" />,
      powerful: <Zap className="h-4 w-4" />
    };

    return typeIcons[type] || <User className="h-4 w-4" />;
  };

  const getCharacterMoodColor = (character) => {
    const mood = character.mood?.toLowerCase() || 'neutral';
    
    const moodColors = {
      happy: 'border-yellow-400 bg-yellow-50 dark:bg-yellow-900/20',
      sad: 'border-blue-400 bg-blue-50 dark:bg-blue-900/20',
      angry: 'border-red-400 bg-red-50 dark:bg-red-900/20',
      determined: 'border-orange-400 bg-orange-50 dark:bg-orange-900/20',
      mysterious: 'border-purple-400 bg-purple-50 dark:bg-purple-900/20',
      romantic: 'border-pink-400 bg-pink-50 dark:bg-pink-900/20',
      confident: 'border-green-400 bg-green-50 dark:bg-green-900/20',
      neutral: 'border-gray-400 bg-gray-50 dark:bg-gray-900/20'
    };

    return moodColors[mood] || moodColors.neutral;
  };

  const getCharacterRelevance = (character) => {
    const suggestion = characterSuggestions.find(s => s.character.name === character.name);
    return suggestion?.relevance || 'normal';
  };

  const getRelevanceIndicator = (relevance) => {
    if (relevance === 'high') {
      return <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse" />;
    } else if (relevance === 'suggested') {
      return <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full" />;
    }
    return null;
  };

  const getSuggestionReason = (character) => {
    const suggestion = characterSuggestions.find(s => s.character.name === character.name);
    return suggestion?.reason || '';
  };

  if (!characters || characters.length === 0) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      {/* Active Character Display */}
      <div className="flex items-center gap-2 mb-2">
        <span className="text-sm font-medium text-muted-foreground">Speaking as:</span>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className={`flex items-center gap-2 px-3 py-2 rounded-lg border-2 transition-all ${
            activeCharacter 
              ? `${getCharacterMoodColor(activeCharacter)} border-primary`
              : 'border-dashed border-muted-foreground bg-muted/50'
          }`}
        >
          <div className="relative">
            {activeCharacter ? getCharacterIcon(activeCharacter) : <User className="h-4 w-4" />}
            {activeCharacter && getRelevanceIndicator(getCharacterRelevance(activeCharacter))}
          </div>
          <span className="font-medium">
            {activeCharacter ? activeCharacter.name : 'Select Character'}
          </span>
          <ChevronDown className={`h-4 w-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
        </button>
      </div>

      {/* Character Selection Dropdown */}
      {isExpanded && (
        <div className="absolute bottom-full left-0 right-0 mb-2 bg-background border border-border rounded-lg shadow-lg z-20 max-h-64 overflow-y-auto">
          <div className="p-2">
            <div className="flex items-center gap-2 mb-2 text-xs text-muted-foreground">
              <Users className="h-3 w-3" />
              <span>Choose your character</span>
            </div>
            
            <div className="space-y-1">
              {characters.map((character) => {
                const relevance = getCharacterRelevance(character);
                const reason = getSuggestionReason(character);
                const isActive = activeCharacter?.name === character.name;
                
                return (
                  <button
                    key={character.name}
                    onClick={() => {
                      onCharacterSelect(character);
                      setIsExpanded(false);
                    }}
                    className={`w-full text-left p-3 rounded-lg border transition-all group ${
                      isActive
                        ? 'border-primary bg-primary/10'
                        : 'border-border hover:border-primary/50 hover:bg-secondary/50'
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div className="relative">
                        <div className={`p-2 rounded-full ${getCharacterMoodColor(character)}`}>
                          {getCharacterIcon(character)}
                        </div>
                        {getRelevanceIndicator(relevance)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-medium group-hover:text-primary transition-colors">
                            {character.name}
                          </span>
                          {relevance === 'high' && (
                            <span className="text-xs bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 px-2 py-0.5 rounded-full">
                              Recommended
                            </span>
                          )}
                          {relevance === 'suggested' && (
                            <span className="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300 px-2 py-0.5 rounded-full">
                              Suggested
                            </span>
                          )}
                        </div>
                        
                        <div className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {character.description}
                        </div>
                        
                        {reason && (
                          <div className="text-xs text-primary mt-1 italic">
                            💡 {reason}
                          </div>
                        )}
                        
                        <div className="flex items-center gap-2 mt-2">
                          <span className={`text-xs px-2 py-0.5 rounded-full ${getCharacterMoodColor(character)}`}>
                            {character.mood || 'neutral'}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {character.type || 'modern'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Character Context Info */}
      {activeCharacter && (
        <div className="text-xs text-muted-foreground mt-1">
          <div className="flex items-center gap-2">
            <span>Mood: {activeCharacter.mood || 'neutral'}</span>
            <span>•</span>
            <span>Type: {activeCharacter.type || 'modern'}</span>
            {getSuggestionReason(activeCharacter) && (
              <>
                <span>•</span>
                <span className="text-primary">💡 {getSuggestionReason(activeCharacter)}</span>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Helper function to generate character suggestions based on context
const generateCharacterSuggestions = (characters, storyArc, chatHistory) => {
  const suggestions = [];
  const recentMessages = chatHistory.slice(-5);
  const currentPhase = storyArc.currentPhase || 'introduction';
  const theme = storyArc.theme || 'general';

  characters.forEach(character => {
    let relevance = 'normal';
    let reason = '';

    // Check if character hasn't spoken recently
    const lastMessage = recentMessages.findLast(msg => msg.speaker === character.name);
    const messagesSinceLastSpoke = lastMessage 
      ? recentMessages.length - recentMessages.indexOf(lastMessage) - 1
      : recentMessages.length;

    if (messagesSinceLastSpoke >= 3) {
      relevance = 'suggested';
      reason = 'Haven\'t heard from them lately';
    }

    // Check if character type matches current theme
    if (character.type === theme) {
      relevance = 'high';
      reason = 'Perfect for current theme';
    }

    // Check if character mood matches story tension
    const tension = storyArc.currentTension || 'medium';
    if (
      (tension === 'high' && ['determined', 'angry', 'intense'].includes(character.mood)) ||
      (tension === 'low' && ['happy', 'calm', 'peaceful'].includes(character.mood))
    ) {
      relevance = 'suggested';
      reason = 'Mood fits the current tension';
    }

    // Check if character has relevant personality for current phase
    if (character.personality) {
      if (currentPhase === 'climax' && character.personality.confidence > 7) {
        relevance = 'high';
        reason = 'Confident personality needed for climax';
      } else if (currentPhase === 'introduction' && character.personality.sociability > 7) {
        relevance = 'suggested';
        reason = 'Great for introductions';
      }
    }

    suggestions.push({
      character,
      relevance,
      reason
    });
  });

  return suggestions;
};

export default SmartCharacterSelector;
