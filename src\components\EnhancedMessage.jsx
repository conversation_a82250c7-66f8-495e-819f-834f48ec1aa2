import React, { useState, useRef, useEffect } from 'react';
import { 
  MoreHorizontal, 
  RefreshC<PERSON>, 
  Co<PERSON>, 
  Smile, 
  Heart, 
  Laugh,
  Reply,
  Share
} from 'lucide-react';

const EnhancedMessage = ({
  message,
  reactions = {},
  onReaction,
  onRegenerate,
  onCopy,
  onReply,
  isMobile = false,
  className = ''
}) => {
  const [showActions, setShowActions] = useState(false);
  const [longPressTimer, setLongPressTimer] = useState(null);
  const [touchStartTime, setTouchStartTime] = useState(null);
  const messageRef = useRef(null);

  // Handle long press for mobile
  const handleTouchStart = (e) => {
    if (!isMobile) return;
    
    setTouchStartTime(Date.now());
    const timer = setTimeout(() => {
      setShowActions(true);
      // Haptic feedback if available
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    }, 500); // 500ms long press
    
    setLongPressTimer(timer);
  };

  const handleTouchEnd = (e) => {
    if (!isMobile) return;
    
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
    
    // If it was a quick tap (less than 200ms), don't show actions
    if (touchStartTime && Date.now() - touchStartTime < 200) {
      setShowActions(false);
    }
  };

  const handleTouchMove = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  };

  // Handle click outside to close actions
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (messageRef.current && !messageRef.current.contains(event.target)) {
        setShowActions(false);
      }
    };

    if (showActions) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showActions]);

  const handleReaction = (reactionType) => {
    onReaction(message.id, reactionType);
    setShowActions(false);
  };

  const handleCopy = () => {
    onCopy(message.message);
    setShowActions(false);
  };

  const handleRegenerate = () => {
    onRegenerate(message.id);
    setShowActions(false);
  };

  const handleReply = () => {
    onReply(message);
    setShowActions(false);
  };

  return (
    <div
      ref={messageRef}
      className={`group flex gap-3 ${message.isUser ? "justify-end" : "justify-start"} ${className}`}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onTouchMove={handleTouchMove}
    >
      <div
        className={`max-w-[85%] sm:max-w-[70%] rounded-lg p-3 relative ${
          message.isUser
            ? "bg-primary text-primary-foreground"
            : "bg-secondary"
        }`}
      >
        {/* Message Header */}
        <div className="flex items-center gap-2 mb-1">
          <span className="text-sm font-medium">{message.speaker}</span>
          <span className="text-xs opacity-70">
            {new Date(message.timestamp).toLocaleTimeString()}
          </span>
          
          {/* Desktop: Show on hover, Mobile: Always show if actions are open */}
          {(!isMobile || showActions) && (
            <button
              onClick={() => setShowActions(!showActions)}
              className={`p-1 rounded-full transition-all ${
                isMobile 
                  ? 'opacity-100' 
                  : 'opacity-0 group-hover:opacity-100'
              } hover:bg-background/20`}
              aria-label="Message actions"
              aria-expanded={showActions}
              aria-haspopup="true"
            >
              <MoreHorizontal className="h-3 w-3" />
            </button>
          )}
        </div>

        {/* Message Content */}
        <p className={`text-sm ${message.isAction ? 'italic text-muted-foreground' : ''}`}>
          {message.message}
        </p>

        {/* Message Reactions Display */}
        {reactions[message.id] && Object.keys(reactions[message.id]).length > 0 && (
          <div className="flex gap-1 mt-2 flex-wrap">
            {Object.entries(reactions[message.id]).map(([reaction, count]) => (
              <button
                key={reaction}
                onClick={() => handleReaction(reaction)}
                className="text-xs bg-background/20 hover:bg-background/30 rounded-full px-2 py-1 flex items-center gap-1 transition-colors"
                aria-label={`React with ${reaction}`}
              >
                {reaction === 'heart' && <Heart className="h-3 w-3" />}
                {reaction === 'smile' && <Smile className="h-3 w-3" />}
                {reaction === 'laugh' && <Laugh className="h-3 w-3" />}
                {count}
              </button>
            ))}
          </div>
        )}

        {/* Message Actions Panel */}
        {showActions && (
          <div className={`absolute ${
            isMobile 
              ? 'bottom-full left-0 right-0 mb-2' 
              : 'top-full left-0 mt-1'
          } bg-background border border-border rounded-lg shadow-lg p-2 z-10 min-w-[200px]`}>
            
            {/* Quick Actions Row */}
            <div className="flex items-center gap-1 mb-2 pb-2 border-b border-border">
              <button
                onClick={() => handleReaction('smile')}
                className="p-2 rounded-full hover:bg-secondary transition-colors"
                aria-label="React with smile"
              >
                <Smile className="h-4 w-4" />
              </button>
              <button
                onClick={() => handleReaction('heart')}
                className="p-2 rounded-full hover:bg-secondary transition-colors"
                aria-label="React with heart"
              >
                <Heart className="h-4 w-4" />
              </button>
              <button
                onClick={() => handleReaction('laugh')}
                className="p-2 rounded-full hover:bg-secondary transition-colors"
                aria-label="React with laugh"
              >
                <Laugh className="h-4 w-4" />
              </button>
            </div>

            {/* Action Buttons */}
            <div className="space-y-1">
              {!message.isUser && onRegenerate && (
                <button
                  onClick={handleRegenerate}
                  className="flex items-center gap-2 w-full px-3 py-2 text-sm hover:bg-secondary rounded text-left transition-colors"
                >
                  <RefreshCw className="h-4 w-4" />
                  Regenerate
                </button>
              )}
              
              <button
                onClick={handleCopy}
                className="flex items-center gap-2 w-full px-3 py-2 text-sm hover:bg-secondary rounded text-left transition-colors"
              >
                <Copy className="h-4 w-4" />
                Copy
              </button>
              
              {onReply && (
                <button
                  onClick={handleReply}
                  className="flex items-center gap-2 w-full px-3 py-2 text-sm hover:bg-secondary rounded text-left transition-colors"
                >
                  <Reply className="h-4 w-4" />
                  Reply
                </button>
              )}
              
              <button
                onClick={() => {
                  // Share functionality
                  if (navigator.share) {
                    navigator.share({
                      text: message.message,
                      title: `Message from ${message.speaker}`
                    });
                  } else {
                    handleCopy();
                  }
                  setShowActions(false);
                }}
                className="flex items-center gap-2 w-full px-3 py-2 text-sm hover:bg-secondary rounded text-left transition-colors"
              >
                <Share className="h-4 w-4" />
                Share
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedMessage;
