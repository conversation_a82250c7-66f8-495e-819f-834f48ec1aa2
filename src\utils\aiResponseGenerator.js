// AI Response Generator for Velora Chat Interface

/**
 * Generate contextually appropriate AI responses based on story arc, character personalities, and chat history
 * @param {Object} params - Parameters for response generation
 * @param {Object} params.storyArc - Current story arc data
 * @param {Object} params.activeCharacter - Character that should respond
 * @param {Array} params.chatHistory - Recent chat messages
 * @param {Object} params.userMessage - The message to respond to
 * @param {Array} params.allCharacters - All available characters
 * @returns {Promise<Object>} Generated AI response
 */
export const generateAIResponse = async ({
  storyArc,
  activeCharacter,
  chatHistory,
  userMessage,
  allCharacters
}) => {
  try {
    // Determine which character should respond
    const respondingCharacter = determineRespondingCharacter({
      storyArc,
      activeCharacter,
      chatHistory,
      userMessage,
      allCharacters
    });

    // Generate response based on character and context
    const response = await generateCharacterResponse({
      character: responding<PERSON><PERSON>cter,
      storyArc,
      chatHistory,
      userMessage,
      allCharacters
    });

    // Update story arc progression
    const updatedStoryArc = updateStoryProgression({
      storyArc,
      userMessage,
      aiResponse: response,
      character: responding<PERSON><PERSON>cter
    });

    return {
      response,
      character: respondingCharacter,
      updatedStoryArc,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error generating AI response:', error);
    return generateFallbackResponse(activeCharacter);
  }
};

/**
 * Determine which character should respond based on context
 */
const determineRespondingCharacter = ({
  storyArc,
  activeCharacter,
  chatHistory,
  userMessage,
  allCharacters
}) => {
  // If user just spoke as a character, another character should respond
  if (userMessage.speaker && userMessage.speaker !== 'User') {
    // Find characters that haven't spoken recently
    const recentSpeakers = chatHistory.slice(-3).map(msg => msg.speaker);
    const availableCharacters = allCharacters.filter(char => 
      char.name !== userMessage.speaker && !recentSpeakers.includes(char.name)
    );

    if (availableCharacters.length > 0) {
      // Choose character based on story context and relationships
      return chooseContextualCharacter(availableCharacters, storyArc, userMessage);
    }
  }

  // Default to a main character or the first available character
  return allCharacters.find(char => char.isMainCharacter) || allCharacters[0];
};

/**
 * Choose character based on story context and relationships
 */
const chooseContextualCharacter = (availableCharacters, storyArc, userMessage) => {
  const currentPhase = storyArc.currentPhase || 'introduction';
  const theme = storyArc.theme || 'general';

  // Phase-based character selection
  if (currentPhase === 'climax') {
    // Prefer confident, powerful characters during climax
    const strongCharacters = availableCharacters.filter(char => 
      char.personality?.confidence > 7 || char.type === 'superhero' || char.type === 'powerful'
    );
    if (strongCharacters.length > 0) return strongCharacters[0];
  }

  if (currentPhase === 'introduction') {
    // Prefer sociable characters for introductions
    const socialCharacters = availableCharacters.filter(char => 
      char.personality?.sociability > 7 || char.personality?.humor > 7
    );
    if (socialCharacters.length > 0) return socialCharacters[0];
  }

  // Theme-based selection
  const themeMatchingCharacters = availableCharacters.filter(char => char.type === theme);
  if (themeMatchingCharacters.length > 0) return themeMatchingCharacters[0];

  // Default to first available
  return availableCharacters[0];
};

/**
 * Generate character-specific response
 */
const generateCharacterResponse = async ({
  character,
  storyArc,
  chatHistory,
  userMessage,
  allCharacters
}) => {
  const context = buildResponseContext({
    character,
    storyArc,
    chatHistory,
    userMessage,
    allCharacters
  });

  // Generate response based on character personality and context
  const responseText = generateResponseText(context);
  
  // Determine if response should be an action or dialogue
  const isAction = shouldBeAction(context);

  return {
    id: generateMessageId(),
    speaker: character.name,
    message: responseText,
    isAction,
    isUser: false,
    timestamp: new Date().toISOString(),
    character: character
  };
};

/**
 * Build context for response generation
 */
const buildResponseContext = ({
  character,
  storyArc,
  chatHistory,
  userMessage,
  allCharacters
}) => {
  const recentMessages = chatHistory.slice(-5);
  const currentPhase = storyArc.currentPhase || 'introduction';
  const tension = storyArc.currentTension || 'medium';
  
  return {
    character,
    personality: character.personality || {},
    mood: character.mood || 'neutral',
    type: character.type || 'modern',
    currentPhase,
    tension,
    theme: storyArc.theme || 'general',
    recentMessages,
    userMessage,
    conversationTone: analyzeConversationTone(recentMessages),
    storyContext: storyArc.context || '',
    relationships: analyzeCharacterRelationships(character, allCharacters, chatHistory)
  };
};

/**
 * Generate response text based on context
 */
const generateResponseText = (context) => {
  const { character, personality, mood, currentPhase, tension, userMessage, conversationTone } = context;

  // Character-specific response patterns
  const responsePatterns = getCharacterResponsePatterns(character);
  
  // Phase-specific responses
  const phaseResponses = getPhaseSpecificResponses(currentPhase, tension);
  
  // Mood-influenced responses
  const moodResponses = getMoodInfluencedResponses(mood);
  
  // Combine patterns to generate contextual response
  return generateContextualResponse({
    patterns: responsePatterns,
    phaseResponses,
    moodResponses,
    userMessage,
    conversationTone,
    character
  });
};

/**
 * Get character-specific response patterns
 */
const getCharacterResponsePatterns = (character) => {
  const patterns = [];
  
  // Add catchphrases if available
  if (character.catchphrases && character.catchphrases.length > 0) {
    patterns.push(...character.catchphrases);
  }
  
  // Personality-based patterns
  if (character.personality) {
    const { analytical, emotional, humor, confidence } = character.personality;
    
    if (analytical > 7) {
      patterns.push("Let me think about this...", "From an analytical perspective...", "The logical approach would be...");
    }
    
    if (emotional > 7) {
      patterns.push("I feel that...", "My heart tells me...", "This really moves me...");
    }
    
    if (humor > 7) {
      patterns.push("You know what's funny about this?", "That reminds me of a joke...", "Well, this is amusing...");
    }
    
    if (confidence > 7) {
      patterns.push("I'm certain that...", "Without a doubt...", "Trust me on this...");
    }
  }
  
  return patterns;
};

/**
 * Get phase-specific responses
 */
const getPhaseSpecificResponses = (phase, tension) => {
  const responses = {
    introduction: [
      "Nice to meet you!", "I'm glad we're here together.", "This seems like an interesting place.",
      "What brings you here?", "I have a good feeling about this.", "Let's see what happens next."
    ],
    rising: [
      "Things are getting interesting...", "I sense something changing.", "We need to be careful here.",
      "This is more complex than I thought.", "Something doesn't feel right.", "We should stay alert."
    ],
    climax: [
      "This is it - the moment of truth!", "Everything depends on what we do now.", "We can't back down now!",
      "It's now or never!", "This is our chance!", "We have to give it everything we've got!"
    ],
    resolution: [
      "I think we did well.", "That was quite an experience.", "I'm glad that's over.",
      "What do we do now?", "I learned something important today.", "We make a good team."
    ]
  };
  
  let phaseResponses = responses[phase] || responses.introduction;
  
  // Modify based on tension
  if (tension === 'high') {
    phaseResponses = phaseResponses.map(response => 
      response.endsWith('.') ? response.slice(0, -1) + '!' : response + '!'
    );
  }
  
  return phaseResponses;
};

/**
 * Get mood-influenced responses
 */
const getMoodInfluencedResponses = (mood) => {
  const moodResponses = {
    happy: ["I'm feeling great about this!", "This is wonderful!", "I love how this is going!"],
    sad: ["I'm not sure about this...", "This makes me feel melancholy.", "I wish things were different."],
    angry: ["This is frustrating!", "I don't like this at all!", "Something needs to change!"],
    determined: ["I won't give up!", "We can do this!", "I'm committed to seeing this through!"],
    mysterious: ["There's more to this than meets the eye...", "I sense hidden depths here.", "Not everything is as it seems."],
    romantic: ["There's something beautiful about this moment.", "I feel a connection here.", "This touches my heart."],
    neutral: ["I see.", "Interesting.", "Let me consider this."]
  };
  
  return moodResponses[mood] || moodResponses.neutral;
};

/**
 * Generate contextual response combining all patterns
 */
const generateContextualResponse = ({
  patterns,
  phaseResponses,
  moodResponses,
  userMessage,
  conversationTone,
  character
}) => {
  // Simple response generation - in a real implementation, this would use more sophisticated NLP
  const allResponses = [...patterns, ...phaseResponses, ...moodResponses];
  
  // Filter responses based on conversation tone and user message
  let relevantResponses = allResponses;
  
  if (userMessage.message) {
    const userText = userMessage.message.toLowerCase();
    
    // If user asked a question, provide an answer
    if (userText.includes('?')) {
      relevantResponses = [
        "That's a great question. I think...",
        "Let me think about that...",
        "From my perspective...",
        "I believe that...",
        "That's something I've wondered about too."
      ];
    }
    
    // If user expressed emotion, respond empathetically
    if (userText.includes('sad') || userText.includes('upset')) {
      relevantResponses = [
        "I'm sorry you're feeling that way.",
        "I understand how you feel.",
        "Is there anything I can do to help?",
        "You don't have to go through this alone."
      ];
    }
    
    if (userText.includes('happy') || userText.includes('excited')) {
      relevantResponses = [
        "I'm so glad to hear that!",
        "Your happiness is contagious!",
        "That's wonderful news!",
        "I love seeing you so positive!"
      ];
    }
  }
  
  // Select a random response from relevant options
  const selectedResponse = relevantResponses[Math.floor(Math.random() * relevantResponses.length)];
  
  return selectedResponse || "I'm not sure what to say about that.";
};

/**
 * Determine if response should be an action
 */
const shouldBeAction = (context) => {
  const { currentPhase, tension, conversationTone } = context;
  
  // Higher chance of actions during high tension or climax
  if (currentPhase === 'climax' || tension === 'high') {
    return Math.random() < 0.4; // 40% chance of action
  }
  
  // Lower chance during introduction or low tension
  if (currentPhase === 'introduction' || tension === 'low') {
    return Math.random() < 0.1; // 10% chance of action
  }
  
  return Math.random() < 0.2; // 20% default chance
};

/**
 * Update story progression based on interaction
 */
const updateStoryProgression = ({ storyArc, userMessage, aiResponse, character }) => {
  const updatedArc = { ...storyArc };
  
  // Increment message count
  updatedArc.messageCount = (updatedArc.messageCount || 0) + 1;
  
  // Update tension based on message content and phase
  updatedArc.currentTension = calculateNewTension(storyArc, userMessage, aiResponse);
  
  // Progress phase if appropriate
  updatedArc.currentPhase = calculatePhaseProgression(storyArc, updatedArc.messageCount);
  
  // Update character involvement
  if (!updatedArc.activeCharacters) updatedArc.activeCharacters = [];
  if (!updatedArc.activeCharacters.includes(character.name)) {
    updatedArc.activeCharacters.push(character.name);
  }
  
  return updatedArc;
};

/**
 * Calculate new tension level
 */
const calculateNewTension = (storyArc, userMessage, aiResponse) => {
  const currentTension = storyArc.currentTension || 'medium';
  const tensionLevels = ['low', 'medium', 'high', 'extreme'];
  const currentIndex = tensionLevels.indexOf(currentTension);
  
  // Analyze message content for tension indicators
  const messageText = (userMessage.message + ' ' + aiResponse.message).toLowerCase();
  
  if (messageText.includes('fight') || messageText.includes('danger') || messageText.includes('urgent')) {
    return tensionLevels[Math.min(currentIndex + 1, tensionLevels.length - 1)];
  }
  
  if (messageText.includes('calm') || messageText.includes('peaceful') || messageText.includes('relax')) {
    return tensionLevels[Math.max(currentIndex - 1, 0)];
  }
  
  return currentTension;
};

/**
 * Calculate phase progression
 */
const calculatePhaseProgression = (storyArc, messageCount) => {
  const currentPhase = storyArc.currentPhase || 'introduction';
  const phases = ['introduction', 'rising', 'climax', 'resolution'];
  
  // Simple progression based on message count
  if (messageCount < 5) return 'introduction';
  if (messageCount < 15) return 'rising';
  if (messageCount < 25) return 'climax';
  return 'resolution';
};

/**
 * Analyze conversation tone
 */
const analyzeConversationTone = (recentMessages) => {
  if (!recentMessages || recentMessages.length === 0) return 'neutral';
  
  const allText = recentMessages.map(msg => msg.message).join(' ').toLowerCase();
  
  if (allText.includes('happy') || allText.includes('joy') || allText.includes('excited')) {
    return 'positive';
  }
  
  if (allText.includes('sad') || allText.includes('angry') || allText.includes('upset')) {
    return 'negative';
  }
  
  if (allText.includes('fight') || allText.includes('battle') || allText.includes('conflict')) {
    return 'tense';
  }
  
  return 'neutral';
};

/**
 * Analyze character relationships
 */
const analyzeCharacterRelationships = (character, allCharacters, chatHistory) => {
  // Simple relationship analysis based on interaction frequency
  const relationships = {};
  
  chatHistory.forEach(msg => {
    if (msg.speaker !== character.name && msg.speaker !== 'User') {
      relationships[msg.speaker] = (relationships[msg.speaker] || 0) + 1;
    }
  });
  
  return relationships;
};

/**
 * Generate fallback response for errors
 */
const generateFallbackResponse = (character) => {
  const fallbackResponses = [
    "I'm not sure what to say about that.",
    "That's interesting...",
    "Let me think about that for a moment.",
    "I need to process what just happened.",
    "This is quite unexpected."
  ];
  
  return {
    id: generateMessageId(),
    speaker: character?.name || 'AI',
    message: fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)],
    isAction: false,
    isUser: false,
    timestamp: new Date().toISOString(),
    character: character
  };
};

/**
 * Generate unique message ID
 */
const generateMessageId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

export default generateAIResponse;
