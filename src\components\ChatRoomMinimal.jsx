import { useState, useEffect, useRef } from "react";
import { useTheme } from "./ThemeProvider";
import {
  ArrowLeft,
  Download,
  Send,
  Settings,
  UserPlus,
  Sparkles,
} from "lucide-react";

const ChatRoomMinimal = ({
  chatRoom,
  chatHistory,
  setChatHistory,
  storyArc,
  setStoryArc,
  onSaveChat,
  onLeaveChat,
  onHowToUse,
}) => {
  const { theme, setTheme } = useTheme();
  const [message, setMessage] = useState("");
  const [activeCharacter, setActiveCharacter] = useState(null);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);

  // Set the first character as active by default
  useEffect(() => {
    if (chatRoom?.characters?.length > 0 && !activeCharacter) {
      setActiveCharacter(chatRoom.characters[0]);
    }
  }, [chatRoom, activeCharacter]);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatHistory]);

  // Handle sending a message
  const handleSendMessage = () => {
    if (!message.trim() || !activeCharacter) return;

    const newMessage = {
      id: Date.now(),
      speaker: activeCharacter.name,
      character: activeCharacter,
      message: message.trim(),
      isUser: true,
      timestamp: new Date().toISOString(),
    };

    setChatHistory([...chatHistory, newMessage]);
    setMessage("");

    // Simulate AI response after a delay
    setTimeout(() => {
      const aiResponse = {
        id: Date.now() + 1,
        speaker: activeCharacter.name,
        character: activeCharacter,
        message: `This is a test response from ${activeCharacter.name}. The chat functionality is working!`,
        isUser: false,
        timestamp: new Date().toISOString(),
      };
      setChatHistory(prev => [...prev, aiResponse]);
    }, 1000);
  };

  // Handle key press in message input
  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!chatRoom) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-bold mb-2">No Chat Room Data</h2>
          <p className="text-muted-foreground mb-4">
            Chat room data is missing. Please try again.
          </p>
          <button
            onClick={onLeaveChat}
            className="bg-primary text-primary-foreground px-4 py-2 rounded-lg"
          >
            Back to Landing
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-3">
          <button
            onClick={onLeaveChat}
            className="p-2 rounded-full hover:bg-secondary/80 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-lg font-semibold">{chatRoom.name}</h1>
            <p className="text-sm text-muted-foreground">
              {chatRoom.characters?.length || 0} characters
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={onSaveChat}
            className="p-2 rounded-full hover:bg-secondary/80 transition-colors"
            title="Save Chat"
          >
            <Download className="h-5 w-5" />
          </button>
          <button
            className="p-2 rounded-full hover:bg-secondary/80 transition-colors"
            title="Settings"
          >
            <Settings className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Character Selection */}
      {chatRoom.characters && chatRoom.characters.length > 0 && (
        <div className="p-4 border-b border-border">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-sm font-medium">Speaking as:</span>
          </div>
          <div className="flex gap-2 flex-wrap">
            {chatRoom.characters.map((character) => (
              <button
                key={character.name}
                onClick={() => setActiveCharacter(character)}
                className={`px-3 py-1.5 rounded-full text-sm transition-colors ${
                  activeCharacter?.name === character.name
                    ? "bg-primary text-primary-foreground"
                    : "bg-secondary hover:bg-secondary/80"
                }`}
              >
                {character.name}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {chatHistory.map((msg) => (
          <div
            key={msg.id}
            className={`flex gap-3 ${msg.isUser ? "justify-end" : "justify-start"}`}
          >
            <div
              className={`max-w-[70%] rounded-lg p-3 ${
                msg.isUser
                  ? "bg-primary text-primary-foreground"
                  : "bg-secondary"
              }`}
            >
              <div className="flex items-center gap-2 mb-1">
                <span className="text-sm font-medium">{msg.speaker}</span>
                <span className="text-xs opacity-70">
                  {new Date(msg.timestamp).toLocaleTimeString()}
                </span>
              </div>
              <p className="text-sm">{msg.message}</p>
            </div>
          </div>
        ))}
        {isTyping && (
          <div className="flex gap-3 justify-start">
            <div className="bg-secondary rounded-lg p-3">
              <div className="flex items-center gap-2">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                </div>
                <span className="text-xs text-muted-foreground">typing...</span>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-border">
        <div className="flex gap-2">
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={`Message as ${activeCharacter?.name || "Character"}...`}
            className="flex-1 resize-none rounded-lg border border-input bg-background px-3 py-2 text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
            rows={1}
            disabled={!activeCharacter}
          />
          <button
            onClick={handleSendMessage}
            disabled={!message.trim() || !activeCharacter}
            className="px-4 py-2 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
        {!activeCharacter && (
          <p className="text-xs text-muted-foreground mt-2">
            Select a character to start chatting
          </p>
        )}
      </div>
    </div>
  );
};

export default ChatRoomMinimal;
