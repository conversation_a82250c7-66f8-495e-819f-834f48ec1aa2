import { useState, useEffect, useRef } from "react";
import { useTheme } from "./ThemeProvider";
import {
  ArrowLeft,
  Download,
  Send,
  Settings,
  RefreshCw,
  <PERSON>,
  Heart,
  Laugh,
  <PERSON>rk<PERSON>,
  Copy,
  MoreHorizontal,
  Users,
} from "lucide-react";
import EnhancedCharacterSelector from "./EnhancedCharacterSelector";
import ContextualSuggestionPanel from "./ContextualSuggestionPanel";
import EnhancedMessage from "./EnhancedMessage";
import FloatingActionButton from "./FloatingActionButton";
import ImmersionFeatures from "./ImmersionFeatures";
import { generateAIResponse } from "../utils/aiResponseGenerator";

const ChatRoomMinimal = ({
  chatRoom,
  chatHistory,
  setChatHistory,
  storyArc,
  setStoryArc,
  onSaveChat,
  onLeaveChat,
  onHowToUse,
}) => {
  const { theme, setTheme } = useTheme();
  const [message, setMessage] = useState("");
  const [activeCharacter, setActiveCharacter] = useState(null);
  const [isTyping, setIsTyping] = useState(false);
  const [showMessageActions, setShowMessageActions] = useState(null);
  const [reactions, setReactions] = useState({});
  const [showSuggestions, setShowSuggestions] = useState(false); // Start collapsed for better UX
  const [isMobile, setIsMobile] = useState(false);
  const [showCharacterSelector, setShowCharacterSelector] = useState(false);
  const [suggestionMode, setSuggestionMode] = useState('contextual'); // 'contextual' | 'character' | 'story'
  const [immersionEnabled, setImmersionEnabled] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(false);
  const [keyboardShortcutsEnabled, setKeyboardShortcutsEnabled] = useState(true);
  const messagesEndRef = useRef(null);

  // Set the first character as active by default
  useEffect(() => {
    if (chatRoom?.characters?.length > 0 && !activeCharacter) {
      setActiveCharacter(chatRoom.characters[0]);
    }
  }, [chatRoom, activeCharacter]);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatHistory]);

  // Mobile detection and responsive behavior
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      // Auto-hide suggestions on mobile for better UX
      if (window.innerWidth < 768) {
        setShowSuggestions(false);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Close overlays when clicking outside (mobile UX improvement)
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showMessageActions && !event.target.closest('.message-actions')) {
        setShowMessageActions(null);
      }
      if (showCharacterSelector && !event.target.closest('.character-selector')) {
        setShowCharacterSelector(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [showMessageActions, showCharacterSelector]);

  // Close message actions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showMessageActions && !event.target.closest('.message-actions')) {
        setShowMessageActions(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showMessageActions]);

  // Handle sending a message with AI response generation
  const handleSendMessage = async () => {
    if (!message.trim() || !activeCharacter || message.length > 1000) return;

    const trimmedMessage = message.trim();
    const isAction = trimmedMessage.startsWith('*') && trimmedMessage.endsWith('*');

    const newMessage = {
      id: Date.now(),
      speaker: activeCharacter.name,
      character: activeCharacter,
      message: trimmedMessage,
      isUser: true,
      isAction: isAction,
      timestamp: new Date().toISOString(),
    };

    // Add user message to chat history
    const updatedHistory = [...chatHistory, newMessage];
    setChatHistory(updatedHistory);
    setMessage("");

    // Generate AI response
    setIsTyping(true);

    try {
      const aiResponseData = await generateAIResponse({
        storyArc,
        activeCharacter,
        chatHistory: updatedHistory,
        userMessage: newMessage,
        allCharacters: chatRoom.characters || []
      });

      // Update story arc if provided
      if (aiResponseData.updatedStoryArc) {
        setStoryArc(aiResponseData.updatedStoryArc);
      }

      // Add AI response to chat with realistic delay
      setTimeout(() => {
        setChatHistory(prev => [...prev, aiResponseData.response]);
        setIsTyping(false);
      }, 1000 + Math.random() * 1000); // Variable delay for realism

    } catch (error) {
      console.error('Error generating AI response:', error);

      // Fallback response
      setTimeout(() => {
        const fallbackResponse = {
          id: Date.now() + 1,
          speaker: "AI Assistant",
          message: "I'm processing what you said. Give me a moment to respond appropriately.",
          isUser: false,
          isAction: false,
          timestamp: new Date().toISOString(),
        };
        setChatHistory(prev => [...prev, fallbackResponse]);
        setIsTyping(false);
      }, 1500);
    }
  };

  // Handle key press in message input
  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Handle message regeneration with AI
  const handleRegenerateMessage = async (messageId) => {
    const messageIndex = chatHistory.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1) return;

    const messageToRegenerate = chatHistory[messageIndex];
    if (messageToRegenerate.isUser) return; // Don't regenerate user messages

    setIsTyping(true);

    try {
      // Find the user message that prompted this AI response
      const previousUserMessage = chatHistory.slice(0, messageIndex).reverse().find(msg => msg.isUser);

      if (previousUserMessage) {
        const aiResponseData = await generateAIResponse({
          storyArc,
          activeCharacter: messageToRegenerate.character || activeCharacter,
          chatHistory: chatHistory.slice(0, messageIndex),
          userMessage: previousUserMessage,
          allCharacters: chatRoom.characters || []
        });

        // Update the message in place
        setTimeout(() => {
          const regeneratedMessage = {
            ...messageToRegenerate,
            message: aiResponseData.response.message,
            timestamp: new Date().toISOString(),
          };

          const newHistory = [...chatHistory];
          newHistory[messageIndex] = regeneratedMessage;
          setChatHistory(newHistory);
          setIsTyping(false);
        }, 1000);
      } else {
        // Fallback if no previous user message found
        setTimeout(() => {
          const regeneratedMessage = {
            ...messageToRegenerate,
            message: `[Regenerated] ${messageToRegenerate.character?.name || 'Character'} responds differently to the conversation.`,
            timestamp: new Date().toISOString(),
          };

          const newHistory = [...chatHistory];
          newHistory[messageIndex] = regeneratedMessage;
          setChatHistory(newHistory);
          setIsTyping(false);
        }, 1500);
      }
    } catch (error) {
      console.error('Error regenerating message:', error);
      setIsTyping(false);
    }
  };

  // Handle message reactions
  const handleReaction = (messageId, reactionType) => {
    setReactions(prev => ({
      ...prev,
      [messageId]: {
        ...prev[messageId],
        [reactionType]: (prev[messageId]?.[reactionType] || 0) + 1
      }
    }));
  };

  // Handle copy message
  const handleCopyMessage = (message) => {
    navigator.clipboard.writeText(message).then(() => {
      // Could add a toast notification here
      console.log('Message copied to clipboard');
    });
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion) => {
    if (suggestion.type === 'action') {
      setMessage(`*${suggestion.text}*`);
    } else {
      setMessage(suggestion.text);
    }
    // Auto-focus input after suggestion selection
    setTimeout(() => {
      const textarea = document.querySelector('textarea[placeholder*="Message as"]');
      if (textarea) textarea.focus();
    }, 100);
  };

  // Handle reply to message
  const handleReplyToMessage = (originalMessage) => {
    setMessage(`@${originalMessage.speaker} `);
    // Focus input
    setTimeout(() => {
      const textarea = document.querySelector('textarea[placeholder*="Message as"]');
      if (textarea) textarea.focus();
    }, 100);
  };

  // Keyboard shortcuts
  useEffect(() => {
    if (!keyboardShortcutsEnabled) return;

    const handleKeyDown = (event) => {
      // Ctrl/Cmd + / - Show keyboard shortcuts help
      if ((event.ctrlKey || event.metaKey) && event.key === '/') {
        event.preventDefault();
        // Could show help modal
        console.log('Keyboard shortcuts help');
      }

      // Escape - Close overlays
      if (event.key === 'Escape') {
        setShowMessageActions(null);
        setShowCharacterSelector(false);
        if (isMobile) {
          setShowSuggestions(false);
        }
      }

      // Ctrl/Cmd + K - Quick character switch
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        setShowCharacterSelector(true);
      }

      // Ctrl/Cmd + ; - Toggle suggestions
      if ((event.ctrlKey || event.metaKey) && event.key === ';') {
        event.preventDefault();
        setShowSuggestions(!showSuggestions);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [keyboardShortcutsEnabled, showSuggestions, isMobile]);

  if (!chatRoom) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-bold mb-2">No Chat Room Data</h2>
          <p className="text-muted-foreground mb-4">
            Chat room data is missing. Please try again.
          </p>
          <button
            onClick={onLeaveChat}
            className="bg-primary text-primary-foreground px-4 py-2 rounded-lg"
          >
            Back to Landing
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-background relative overflow-hidden">
      {/* Immersion Features Overlay */}
      {immersionEnabled && (
        <ImmersionFeatures
          storyArc={storyArc}
          activeCharacter={activeCharacter}
          chatHistory={chatHistory}
          className="absolute inset-0 z-0"
        />
      )}

      {/* Main Content */}
      <div className="relative z-10 flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border bg-background/90 backdrop-blur-sm shrink-0">
        <div className="flex items-center gap-3">
          <button
            onClick={onLeaveChat}
            className="p-2 rounded-full hover:bg-secondary/80 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-lg font-semibold">{chatRoom.name}</h1>
            <p className="text-sm text-muted-foreground">
              {chatRoom.characters?.length || 0} characters
            </p>
          </div>
        </div>
          <div className="flex items-center gap-2">
            {/* Desktop-only header controls */}
            {!isMobile && (
              <>
                <button
                  onClick={() => setShowSuggestions(!showSuggestions)}
                  className={`p-2 rounded-full transition-colors ${
                    showSuggestions
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-secondary/80'
                  }`}
                  title={showSuggestions ? "Hide suggestions (Ctrl+;)" : "Show suggestions (Ctrl+;)"}
                >
                  <Sparkles className="h-5 w-5" />
                </button>
                <button
                  onClick={() => setShowCharacterSelector(!showCharacterSelector)}
                  className={`p-2 rounded-full transition-colors ${
                    showCharacterSelector
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-secondary/80'
                  }`}
                  title="Character selector (Ctrl+K)"
                >
                  <Users className="h-5 w-5" />
                </button>
              </>
            )}

            <button
              onClick={onSaveChat}
              className="p-2 rounded-full hover:bg-secondary/80 transition-colors"
              title="Save Chat"
            >
              <Download className="h-5 w-5" />
            </button>
            <button
              className="p-2 rounded-full hover:bg-secondary/80 transition-colors"
              title="Settings"
            >
              <Settings className="h-5 w-5" />
            </button>
          </div>
      </div>

        {/* Main Chat Area */}
        <div className={`flex flex-1 overflow-hidden ${isMobile ? 'flex-col' : ''}`}>
          {/* Messages Area */}
          <div className="flex-1 flex flex-col min-w-0">
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-background/30 backdrop-blur-sm">
              {chatHistory.map((msg, index) => (
                <EnhancedMessage
                  key={msg.id}
                  message={msg}
                  reactions={reactions}
                  onReaction={handleReaction}
                  onRegenerate={!msg.isUser ? handleRegenerateMessage : undefined}
                  onCopy={handleCopyMessage}
                  onReply={handleReplyToMessage}
                  isMobile={isMobile}
                />
              ))}
              {isTyping && (
                <div className="flex gap-3 justify-start">
                  <div className="bg-secondary rounded-lg p-3">
                    <div className="flex items-center gap-2">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                      </div>
                      <span className="text-xs text-muted-foreground">typing...</span>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Input Area */}
            <div className="p-4 border-t border-border bg-background/90 backdrop-blur-sm shrink-0">
              {/* Enhanced Character Selector */}
              {!isMobile && (
                <div className="mb-3">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm font-medium text-muted-foreground">Speaking as:</span>
                    <button
                      onClick={() => setShowCharacterSelector(!showCharacterSelector)}
                      className={`flex items-center gap-2 px-3 py-2 rounded-lg border-2 transition-all ${
                        activeCharacter
                          ? 'border-primary bg-primary/10'
                          : 'border-dashed border-muted-foreground bg-muted/50'
                      }`}
                    >
                      <Users className="h-4 w-4" />
                      <span className="font-medium">
                        {activeCharacter ? activeCharacter.name : 'Select Character'}
                      </span>
                    </button>
                  </div>

                  <EnhancedCharacterSelector
                    characters={chatRoom.characters}
                    activeCharacter={activeCharacter}
                    onCharacterSelect={setActiveCharacter}
                    storyArc={storyArc}
                    chatHistory={chatHistory}
                    isMobile={false}
                    isOpen={showCharacterSelector}
                    onClose={() => setShowCharacterSelector(false)}
                    className="character-selector"
                  />
                </div>
              )}

              {/* Mobile Character Pills */}
              {isMobile && chatRoom.characters && chatRoom.characters.length > 0 && (
                <div className="mb-3">
                  <div className="text-sm font-medium text-muted-foreground mb-2">Speaking as:</div>
                  <div className="flex gap-2 flex-wrap">
                    {chatRoom.characters.slice(0, 3).map((character) => (
                      <button
                        key={character.name}
                        onClick={() => setActiveCharacter(character)}
                        className={`px-3 py-1.5 rounded-full text-sm transition-colors ${
                          activeCharacter?.name === character.name
                            ? "bg-primary text-primary-foreground"
                            : "bg-secondary hover:bg-secondary/80"
                        }`}
                      >
                        {character.name}
                      </button>
                    ))}
                    {chatRoom.characters.length > 3 && (
                      <button
                        onClick={() => setShowCharacterSelector(true)}
                        className="px-3 py-1.5 rounded-full text-sm bg-secondary hover:bg-secondary/80 transition-colors"
                      >
                        +{chatRoom.characters.length - 3} more
                      </button>
                    )}
                  </div>
                </div>
              )}

              {/* Message Input */}
              <div className="flex gap-2">
                <div className="flex-1 relative">
                  <textarea
                    value={message}
                    onChange={(e) => {
                      if (e.target.value.length <= 1000) {
                        setMessage(e.target.value);
                      }
                    }}
                    onKeyPress={handleKeyPress}
                    placeholder={`Message as ${activeCharacter?.name || "Character"}...`}
                    className="w-full resize-none rounded-lg border border-input bg-background px-3 py-2 pr-12 text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    rows={1}
                    disabled={!activeCharacter}
                    maxLength={1000}
                  />
                  {/* Quick action button inside input */}
                  <button
                    onClick={() => setMessage(prev => prev + "*")}
                    className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full hover:bg-secondary/50 text-muted-foreground transition-colors"
                    title="Add action markers (*text*)"
                    disabled={!activeCharacter}
                  >
                    <Sparkles className="h-4 w-4" />
                  </button>
                </div>
                <button
                  onClick={handleSendMessage}
                  disabled={!message.trim() || !activeCharacter || message.length > 1000}
                  className="px-4 py-2 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Send className="h-4 w-4" />
                </button>
              </div>

              {/* Input help text */}
              <div className="flex justify-between items-center mt-2">
                {!activeCharacter ? (
                  <p className="text-xs text-muted-foreground">
                    Select a character to start chatting
                  </p>
                ) : (
                  <p className="text-xs text-muted-foreground">
                    Use *text* for actions • Enter to send • Shift+Enter for new line
                  </p>
                )}
                <span className={`text-xs ${
                  message.length > 900 ? 'text-destructive' :
                  message.length > 800 ? 'text-yellow-500' :
                  'text-muted-foreground'
                }`}>
                  {message.length}/1000
                </span>
              </div>
            </div>
          </div>

          {/* Desktop Sidebar with Suggestions */}
          {!isMobile && showSuggestions && (
            <div className="w-80 border-l border-border bg-background/90 backdrop-blur-sm shrink-0">
              <ContextualSuggestionPanel
                storyArc={storyArc}
                chatHistory={chatHistory}
                activeCharacter={activeCharacter}
                onSuggestionSelect={handleSuggestionSelect}
                isMobile={false}
                isOpen={true}
                mode={suggestionMode}
                className="h-full"
              />
            </div>
          )}
        </div>

        {/* Mobile Character Selector */}
        <EnhancedCharacterSelector
          characters={chatRoom.characters}
          activeCharacter={activeCharacter}
          onCharacterSelect={setActiveCharacter}
          storyArc={storyArc}
          chatHistory={chatHistory}
          isMobile={true}
          isOpen={showCharacterSelector}
          onClose={() => setShowCharacterSelector(false)}
        />

        {/* Mobile Suggestion Panel */}
        <ContextualSuggestionPanel
          storyArc={storyArc}
          chatHistory={chatHistory}
          activeCharacter={activeCharacter}
          onSuggestionSelect={handleSuggestionSelect}
          isMobile={true}
          isOpen={showSuggestions}
          onClose={() => setShowSuggestions(false)}
          mode={suggestionMode}
        />

        {/* Floating Action Button */}
        <FloatingActionButton
          onSuggestionsToggle={() => setShowSuggestions(!showSuggestions)}
          onCharacterSelect={() => setShowCharacterSelector(true)}
          onImmersionToggle={() => setImmersionEnabled(!immersionEnabled)}
          onSoundToggle={() => setSoundEnabled(!soundEnabled)}
          showSuggestions={showSuggestions}
          immersionEnabled={immersionEnabled}
          soundEnabled={soundEnabled}
          isMobile={isMobile}
        />
      </div>
    </div>
  );
};

export default ChatRoomMinimal;
