import { useState, useEffect, useRef } from "react";
import { useTheme } from "./ThemeProvider";
import {
  ArrowLeft,
  Download,
  Send,
  Settings,
  RefreshCw,
  Smile,
  Heart,
  Laugh,
  <PERSON>rk<PERSON>,
  Copy,
  MoreHorizontal,
} from "lucide-react";
import IntelligentSuggestionPanel from "./IntelligentSuggestionPanel";
import ImmersionFeatures from "./ImmersionFeatures";
import SmartCharacterSelector from "./SmartCharacterSelector";

const ChatRoomMinimal = ({
  chatRoom,
  chatHistory,
  setChatHistory,
  storyArc,
  setStoryArc,
  onSaveChat,
  onLeaveChat,
  onHowToUse,
}) => {
  const { theme, setTheme } = useTheme();
  const [message, setMessage] = useState("");
  const [activeCharacter, setActiveCharacter] = useState(null);
  const [isTyping, setIsTyping] = useState(false);
  const [showMessageActions, setShowMessageActions] = useState(null);
  const [reactions, setReactions] = useState({});
  const [showSuggestions, setShowSuggestions] = useState(true);
  const messagesEndRef = useRef(null);

  // Set the first character as active by default
  useEffect(() => {
    if (chatRoom?.characters?.length > 0 && !activeCharacter) {
      setActiveCharacter(chatRoom.characters[0]);
    }
  }, [chatRoom, activeCharacter]);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatHistory]);

  // Close message actions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showMessageActions && !event.target.closest('.message-actions')) {
        setShowMessageActions(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showMessageActions]);

  // Handle sending a message
  const handleSendMessage = () => {
    if (!message.trim() || !activeCharacter || message.length > 1000) return;

    const trimmedMessage = message.trim();
    const isAction = trimmedMessage.startsWith('*') && trimmedMessage.endsWith('*');

    const newMessage = {
      id: Date.now(),
      speaker: activeCharacter.name,
      character: activeCharacter,
      message: trimmedMessage,
      isUser: true,
      isAction: isAction,
      timestamp: new Date().toISOString(),
    };

    setChatHistory([...chatHistory, newMessage]);
    setMessage("");

    // Simulate AI response after a delay (only for non-action messages)
    if (!isAction) {
      setIsTyping(true);
      setTimeout(() => {
        const aiResponse = {
          id: Date.now() + 1,
          speaker: activeCharacter.name,
          character: activeCharacter,
          message: `This is a test response from ${activeCharacter.name}. The chat functionality is working!`,
          isUser: false,
          timestamp: new Date().toISOString(),
        };
        setChatHistory(prev => [...prev, aiResponse]);
        setIsTyping(false);
      }, 1500);
    }
  };

  // Handle key press in message input
  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Handle message regeneration
  const handleRegenerateMessage = (messageId) => {
    const messageIndex = chatHistory.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1) return;

    const messageToRegenerate = chatHistory[messageIndex];
    if (messageToRegenerate.isUser) return; // Don't regenerate user messages

    setIsTyping(true);

    // Simulate regeneration delay
    setTimeout(() => {
      const regeneratedMessage = {
        ...messageToRegenerate,
        message: `[Regenerated] ${messageToRegenerate.character?.name || 'Character'} responds differently: This is an alternative response to show the regeneration feature working.`,
        timestamp: new Date().toISOString(),
      };

      const newHistory = [...chatHistory];
      newHistory[messageIndex] = regeneratedMessage;
      setChatHistory(newHistory);
      setIsTyping(false);
    }, 1500);
  };

  // Handle message reactions
  const handleReaction = (messageId, reactionType) => {
    setReactions(prev => ({
      ...prev,
      [messageId]: {
        ...prev[messageId],
        [reactionType]: (prev[messageId]?.[reactionType] || 0) + 1
      }
    }));
  };

  // Handle copy message
  const handleCopyMessage = (message) => {
    navigator.clipboard.writeText(message).then(() => {
      // Could add a toast notification here
      console.log('Message copied to clipboard');
    });
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion) => {
    if (suggestion.type === 'action') {
      setMessage(`*${suggestion.text}*`);
    } else {
      setMessage(suggestion.text);
    }
  };

  if (!chatRoom) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-bold mb-2">No Chat Room Data</h2>
          <p className="text-muted-foreground mb-4">
            Chat room data is missing. Please try again.
          </p>
          <button
            onClick={onLeaveChat}
            className="bg-primary text-primary-foreground px-4 py-2 rounded-lg"
          >
            Back to Landing
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-background relative">
      {/* Immersion Features Overlay */}
      <ImmersionFeatures
        storyArc={storyArc}
        activeCharacter={activeCharacter}
        chatHistory={chatHistory}
        className="absolute inset-0 z-0"
      />

      {/* Main Content */}
      <div className="relative z-10 flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border bg-background/80 backdrop-blur-sm">
        <div className="flex items-center gap-3">
          <button
            onClick={onLeaveChat}
            className="p-2 rounded-full hover:bg-secondary/80 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-lg font-semibold">{chatRoom.name}</h1>
            <p className="text-sm text-muted-foreground">
              {chatRoom.characters?.length || 0} characters
            </p>
          </div>
        </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowSuggestions(!showSuggestions)}
              className={`p-2 rounded-full transition-colors ${
                showSuggestions
                  ? 'bg-primary text-primary-foreground'
                  : 'hover:bg-secondary/80'
              }`}
              title={showSuggestions ? "Hide suggestions" : "Show suggestions"}
            >
              <Sparkles className="h-5 w-5" />
            </button>
            <button
              onClick={onSaveChat}
              className="p-2 rounded-full hover:bg-secondary/80 transition-colors"
              title="Save Chat"
            >
              <Download className="h-5 w-5" />
            </button>
            <button
              className="p-2 rounded-full hover:bg-secondary/80 transition-colors"
              title="Settings"
            >
              <Settings className="h-5 w-5" />
            </button>
          </div>
      </div>

        {/* Main Chat Area with Sidebar */}
        <div className="flex flex-1 overflow-hidden">
          {/* Messages Area */}
          <div className="flex-1 flex flex-col">
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-background/50 backdrop-blur-sm">
              {chatHistory.map((msg, index) => (
                <div
                  key={msg.id}
                  className={`group flex gap-3 ${msg.isUser ? "justify-end" : "justify-start"}`}
                >
                  <div
                    className={`max-w-[70%] rounded-lg p-3 relative ${
                      msg.isUser
                        ? "bg-primary text-primary-foreground"
                        : "bg-secondary"
                    }`}
                  >
              <div className="flex items-center gap-2 mb-1">
                <span className="text-sm font-medium">{msg.speaker}</span>
                <span className="text-xs opacity-70">
                  {new Date(msg.timestamp).toLocaleTimeString()}
                </span>
                {/* Message actions button */}
                <button
                  onClick={() => setShowMessageActions(showMessageActions === msg.id ? null : msg.id)}
                  className="opacity-0 group-hover:opacity-100 p-1 rounded-full hover:bg-background/20 transition-all"
                  title="Message actions"
                >
                  <MoreHorizontal className="h-3 w-3" />
                </button>
              </div>
              <p className={`text-sm ${msg.isAction ? 'italic text-muted-foreground' : ''}`}>
                {msg.message}
              </p>

              {/* Message reactions display */}
              {reactions[msg.id] && Object.keys(reactions[msg.id]).length > 0 && (
                <div className="flex gap-1 mt-2 flex-wrap">
                  {Object.entries(reactions[msg.id]).map(([reaction, count]) => (
                    <span
                      key={reaction}
                      className="text-xs bg-background/20 rounded-full px-2 py-1 flex items-center gap-1"
                    >
                      {reaction === 'heart' && <Heart className="h-3 w-3" />}
                      {reaction === 'smile' && <Smile className="h-3 w-3" />}
                      {reaction === 'laugh' && <Laugh className="h-3 w-3" />}
                      {count}
                    </span>
                  ))}
                </div>
              )}

              {/* Message actions dropdown */}
              {showMessageActions === msg.id && (
                <div className="message-actions absolute top-full left-0 mt-1 bg-background border border-border rounded-lg shadow-lg p-2 z-10 min-w-[150px]">
                  <div className="flex flex-col gap-1">
                    {!msg.isUser && (
                      <button
                        onClick={() => {
                          handleRegenerateMessage(msg.id);
                          setShowMessageActions(null);
                        }}
                        className="flex items-center gap-2 px-2 py-1 text-sm hover:bg-secondary rounded text-left"
                      >
                        <RefreshCw className="h-3 w-3" />
                        Regenerate
                      </button>
                    )}
                    <button
                      onClick={() => {
                        handleCopyMessage(msg.message);
                        setShowMessageActions(null);
                      }}
                      className="flex items-center gap-2 px-2 py-1 text-sm hover:bg-secondary rounded text-left"
                    >
                      <Copy className="h-3 w-3" />
                      Copy
                    </button>
                    <div className="border-t border-border my-1"></div>
                    <div className="text-xs text-muted-foreground px-2 py-1">React:</div>
                    <div className="flex gap-1 px-2">
                      <button
                        onClick={() => {
                          handleReaction(msg.id, 'smile');
                          setShowMessageActions(null);
                        }}
                        className="p-1 hover:bg-secondary rounded"
                        title="Smile"
                      >
                        <Smile className="h-3 w-3" />
                      </button>
                      <button
                        onClick={() => {
                          handleReaction(msg.id, 'heart');
                          setShowMessageActions(null);
                        }}
                        className="p-1 hover:bg-secondary rounded"
                        title="Love"
                      >
                        <Heart className="h-3 w-3" />
                      </button>
                      <button
                        onClick={() => {
                          handleReaction(msg.id, 'laugh');
                          setShowMessageActions(null);
                        }}
                        className="p-1 hover:bg-secondary rounded"
                        title="Laugh"
                      >
                        <Laugh className="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
              ))}
              {isTyping && (
                <div className="flex gap-3 justify-start">
                  <div className="bg-secondary rounded-lg p-3">
                    <div className="flex items-center gap-2">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                      </div>
                      <span className="text-xs text-muted-foreground">typing...</span>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Input Area */}
            <div className="p-4 border-t border-border bg-background/80 backdrop-blur-sm">
              {/* Smart Character Selector */}
              <SmartCharacterSelector
                characters={chatRoom.characters}
                activeCharacter={activeCharacter}
                onCharacterSelect={setActiveCharacter}
                storyArc={storyArc}
                chatHistory={chatHistory}
                className="mb-3"
              />

              {/* Message Input */}
              <div className="flex gap-2">
                <div className="flex-1 relative">
                  <textarea
                    value={message}
                    onChange={(e) => {
                      if (e.target.value.length <= 1000) {
                        setMessage(e.target.value);
                      }
                    }}
                    onKeyPress={handleKeyPress}
                    placeholder={`Message as ${activeCharacter?.name || "Character"}...`}
                    className="w-full resize-none rounded-lg border border-input bg-background px-3 py-2 pr-12 text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    rows={1}
                    disabled={!activeCharacter}
                    maxLength={1000}
                  />
                  {/* Quick action button inside input */}
                  <button
                    onClick={() => setMessage(prev => prev + "*")}
                    className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full hover:bg-secondary/50 text-muted-foreground transition-colors"
                    title="Add action markers (*text*)"
                    disabled={!activeCharacter}
                  >
                    <Sparkles className="h-4 w-4" />
                  </button>
                </div>
                <button
                  onClick={handleSendMessage}
                  disabled={!message.trim() || !activeCharacter || message.length > 1000}
                  className="px-4 py-2 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Send className="h-4 w-4" />
                </button>
              </div>

              {/* Input help text */}
              <div className="flex justify-between items-center mt-2">
                {!activeCharacter ? (
                  <p className="text-xs text-muted-foreground">
                    Select a character to start chatting
                  </p>
                ) : (
                  <p className="text-xs text-muted-foreground">
                    Use *text* for actions • Enter to send • Shift+Enter for new line
                  </p>
                )}
                <span className={`text-xs ${
                  message.length > 900 ? 'text-destructive' :
                  message.length > 800 ? 'text-yellow-500' :
                  'text-muted-foreground'
                }`}>
                  {message.length}/1000
                </span>
              </div>
            </div>
          </div>

          {/* Sidebar with Suggestions */}
          {showSuggestions && (
            <div className="w-80 border-l border-border bg-background/80 backdrop-blur-sm">
              <IntelligentSuggestionPanel
                storyArc={storyArc}
                chatHistory={chatHistory}
                activeCharacter={activeCharacter}
                onSuggestionSelect={handleSuggestionSelect}
                className="h-full"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatRoomMinimal;
