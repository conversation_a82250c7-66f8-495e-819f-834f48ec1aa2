// Intelligent suggestion system for Velora chat interface

/**
 * Generate context-aware story suggestions based on current chat state
 * @param {Object} storyArc - Current story arc
 * @param {Array} chatHistory - Recent chat messages
 * @param {Object} activeCharacter - Currently selected character
 * @param {string} timeOfDay - Current time context (day/night)
 * @returns {Array} Array of story suggestions
 */
export const generateContextAwareSuggestions = (storyArc, chatHistory, activeCharacter, timeOfDay = 'day') => {
  if (!storyArc || !activeCharacter) return [];

  const recentMessages = chatHistory.slice(-5); // Last 5 messages for context
  const currentPhase = storyArc.currentPhase || 'introduction';
  const tension = storyArc.currentTension || 'medium';
  const theme = storyArc.theme || 'general';

  // Analyze conversation context
  const conversationContext = analyzeConversationContext(recentMessages);
  
  // Generate suggestions based on multiple factors
  const suggestions = [];

  // Phase-based suggestions
  suggestions.push(...getPhaseBasedSuggestions(currentPhase, tension));
  
  // Theme-specific suggestions
  suggestions.push(...getThemeSpecificSuggestions(theme, timeOfDay));
  
  // Context-aware suggestions
  suggestions.push(...getContextualSuggestions(conversationContext, storyArc));
  
  // Character relationship suggestions
  suggestions.push(...getRelationshipSuggestions(activeCharacter, storyArc.keyCharacters));

  return suggestions.slice(0, 6); // Return top 6 suggestions
};

/**
 * Generate character-specific action suggestions
 * @param {Object} character - Character object
 * @param {Object} storyArc - Current story arc
 * @param {Array} chatHistory - Recent chat messages
 * @returns {Array} Array of character-specific actions
 */
export const generateCharacterSpecificActions = (character, storyArc, chatHistory) => {
  if (!character) return [];

  const characterType = character.type || 'modern';
  const mood = character.mood || 'neutral';
  const personality = character.personality || {};
  const currentPhase = storyArc?.currentPhase || 'introduction';

  const actions = [];

  // Personality-driven actions
  actions.push(...getPersonalityBasedActions(personality, mood));
  
  // Type-specific actions
  actions.push(...getTypeSpecificActions(characterType, currentPhase));
  
  // Mood-influenced actions
  actions.push(...getMoodInfluencedActions(mood, character.name));
  
  // Character-specific signature actions
  if (character.catchphrases && character.catchphrases.length > 0) {
    actions.push({
      text: `Say "${character.catchphrases[0]}"`,
      type: 'dialogue',
      category: 'signature'
    });
  }

  return actions.slice(0, 8); // Return top 8 actions
};

/**
 * Analyze recent conversation to understand context
 * @param {Array} recentMessages - Last few messages
 * @returns {Object} Context analysis
 */
const analyzeConversationContext = (recentMessages) => {
  const context = {
    isConflict: false,
    isEmotional: false,
    isAction: false,
    isDialogue: false,
    dominantMood: 'neutral',
    lastSpeaker: null
  };

  if (!recentMessages || recentMessages.length === 0) return context;

  // Analyze message patterns
  recentMessages.forEach(msg => {
    if (msg.isAction) context.isAction = true;
    if (!msg.isAction) context.isDialogue = true;
    
    // Check for conflict keywords
    if (msg.message && /fight|attack|battle|conflict|angry|rage/.test(msg.message.toLowerCase())) {
      context.isConflict = true;
    }
    
    // Check for emotional keywords
    if (msg.message && /love|sad|happy|excited|fear|joy|tears/.test(msg.message.toLowerCase())) {
      context.isEmotional = true;
    }
  });

  context.lastSpeaker = recentMessages[recentMessages.length - 1]?.speaker;
  return context;
};

/**
 * Get suggestions based on narrative phase
 * @param {string} phase - Current narrative phase
 * @param {string} tension - Current tension level
 * @returns {Array} Phase-based suggestions
 */
const getPhaseBasedSuggestions = (phase, tension) => {
  const suggestions = {
    introduction: [
      { text: "Introduce a new character", type: 'narrative', category: 'character' },
      { text: "Describe the setting in detail", type: 'narrative', category: 'environment' },
      { text: "Reveal character backstory", type: 'dialogue', category: 'development' }
    ],
    rising: [
      { text: "Increase the stakes", type: 'narrative', category: 'tension' },
      { text: "Introduce a complication", type: 'narrative', category: 'plot' },
      { text: "Create character conflict", type: 'dialogue', category: 'conflict' }
    ],
    climax: [
      { text: "Make a crucial decision", type: 'action', category: 'decisive' },
      { text: "Confront the main challenge", type: 'action', category: 'confrontation' },
      { text: "Reveal hidden truth", type: 'dialogue', category: 'revelation' }
    ],
    resolution: [
      { text: "Reflect on what happened", type: 'dialogue', category: 'reflection' },
      { text: "Plan for the future", type: 'dialogue', category: 'planning' },
      { text: "Express gratitude", type: 'dialogue', category: 'emotional' }
    ]
  };

  return suggestions[phase] || suggestions.introduction;
};

/**
 * Get theme-specific suggestions
 * @param {string} theme - Story theme
 * @param {string} timeOfDay - Time context
 * @returns {Array} Theme-specific suggestions
 */
const getThemeSpecificSuggestions = (theme, timeOfDay) => {
  const themeActions = {
    fantasy: [
      { text: "Cast a magical spell", type: 'action', category: 'magic' },
      { text: "Consult ancient wisdom", type: 'dialogue', category: 'lore' },
      { text: "Sense mystical energy", type: 'narrative', category: 'supernatural' }
    ],
    superhero: [
      { text: "Use your superpower", type: 'action', category: 'power' },
      { text: "Protect innocent people", type: 'action', category: 'heroic' },
      { text: "Coordinate with team", type: 'dialogue', category: 'teamwork' }
    ],
    scifi: [
      { text: "Analyze with technology", type: 'action', category: 'tech' },
      { text: "Reference scientific theory", type: 'dialogue', category: 'science' },
      { text: "Scan for anomalies", type: 'action', category: 'investigation' }
    ],
    romance: [
      { text: "Express your feelings", type: 'dialogue', category: 'emotional' },
      { text: "Create romantic moment", type: 'action', category: 'romantic' },
      { text: "Share intimate thought", type: 'dialogue', category: 'intimate' }
    ],
    mystery: [
      { text: "Look for clues", type: 'action', category: 'investigation' },
      { text: "Question someone", type: 'dialogue', category: 'interrogation' },
      { text: "Make a deduction", type: 'dialogue', category: 'reasoning' }
    ]
  };

  const baseActions = themeActions[theme] || themeActions.fantasy;
  
  // Modify suggestions based on time of day
  if (timeOfDay === 'night') {
    baseActions.push(
      { text: "Notice something in the shadows", type: 'narrative', category: 'atmospheric' },
      { text: "Feel the night's energy", type: 'narrative', category: 'mood' }
    );
  } else {
    baseActions.push(
      { text: "Enjoy the daylight", type: 'narrative', category: 'atmospheric' },
      { text: "Feel energized", type: 'narrative', category: 'mood' }
    );
  }

  return baseActions;
};

/**
 * Get contextual suggestions based on conversation analysis
 * @param {Object} context - Conversation context
 * @param {Object} storyArc - Current story arc
 * @returns {Array} Contextual suggestions
 */
const getContextualSuggestions = (context, storyArc) => {
  const suggestions = [];

  if (context.isConflict) {
    suggestions.push(
      { text: "Try to de-escalate", type: 'dialogue', category: 'peaceful' },
      { text: "Stand your ground", type: 'action', category: 'defiant' },
      { text: "Find common ground", type: 'dialogue', category: 'diplomatic' }
    );
  }

  if (context.isEmotional) {
    suggestions.push(
      { text: "Offer comfort", type: 'action', category: 'supportive' },
      { text: "Share your own feelings", type: 'dialogue', category: 'vulnerable' },
      { text: "Give them space", type: 'action', category: 'respectful' }
    );
  }

  if (!context.isAction && !context.isDialogue) {
    suggestions.push(
      { text: "Break the silence", type: 'dialogue', category: 'initiative' },
      { text: "Observe the surroundings", type: 'narrative', category: 'observational' }
    );
  }

  return suggestions;
};

/**
 * Get personality-based actions
 * @param {Object} personality - Character personality traits
 * @param {string} mood - Current mood
 * @returns {Array} Personality-driven actions
 */
const getPersonalityBasedActions = (personality, mood) => {
  const actions = [];

  if (personality.analytical > 7) {
    actions.push({ text: "Analyze the situation logically", type: 'dialogue', category: 'analytical' });
  }
  
  if (personality.emotional > 7) {
    actions.push({ text: "Follow your heart", type: 'action', category: 'emotional' });
  }
  
  if (personality.humor > 7) {
    actions.push({ text: "Make a witty comment", type: 'dialogue', category: 'humorous' });
  }
  
  if (personality.confidence > 7) {
    actions.push({ text: "Take charge of the situation", type: 'action', category: 'leadership' });
  }

  return actions;
};

/**
 * Get type-specific actions
 * @param {string} type - Character type
 * @param {string} phase - Current narrative phase
 * @returns {Array} Type-specific actions
 */
const getTypeSpecificActions = (type, phase) => {
  // Implementation would be similar to existing quickActionUtils but more contextual
  return [];
};

/**
 * Get mood-influenced actions
 * @param {string} mood - Character mood
 * @param {string} characterName - Character name
 * @returns {Array} Mood-based actions
 */
const getMoodInfluencedActions = (mood, characterName) => {
  const moodActions = {
    happy: [
      { text: "Share your joy", type: 'dialogue', category: 'positive' },
      { text: "Encourage others", type: 'dialogue', category: 'supportive' }
    ],
    sad: [
      { text: "Express your sorrow", type: 'dialogue', category: 'vulnerable' },
      { text: "Seek comfort", type: 'action', category: 'needy' }
    ],
    angry: [
      { text: "Voice your frustration", type: 'dialogue', category: 'confrontational' },
      { text: "Take a deep breath", type: 'action', category: 'self-control' }
    ],
    determined: [
      { text: "Push forward with resolve", type: 'action', category: 'determined' },
      { text: "Inspire others", type: 'dialogue', category: 'motivational' }
    ]
  };

  return moodActions[mood] || [];
};

/**
 * Get relationship-based suggestions
 * @param {Object} activeCharacter - Current character
 * @param {Array} keyCharacters - Other important characters
 * @returns {Array} Relationship suggestions
 */
const getRelationshipSuggestions = (activeCharacter, keyCharacters) => {
  if (!keyCharacters || keyCharacters.length === 0) return [];

  return [
    { text: "Address another character directly", type: 'dialogue', category: 'social' },
    { text: "Ask about someone's wellbeing", type: 'dialogue', category: 'caring' },
    { text: "Share a memory together", type: 'dialogue', category: 'nostalgic' }
  ];
};
