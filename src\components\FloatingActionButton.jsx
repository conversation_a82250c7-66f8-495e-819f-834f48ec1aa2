import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  X, 
  <PERSON>rk<PERSON>, 
  Users, 
  Lightbulb, 
  Settings,
  Volume2,
  VolumeX,
  Eye,
  EyeOff,
  Palette
} from 'lucide-react';

const FloatingActionButton = ({
  onSuggestionsToggle,
  onCharacterSelect,
  onImmersionToggle,
  showSuggestions = false,
  immersionEnabled = true,
  soundEnabled = false,
  onSoundToggle,
  isMobile = false,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showTooltip, setShowTooltip] = useState(null);

  // Close expanded menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isExpanded && !event.target.closest('.fab-container')) {
        setIsExpanded(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isExpanded]);

  // Auto-hide tooltip after delay
  useEffect(() => {
    if (showTooltip) {
      const timer = setTimeout(() => setShowTooltip(null), 2000);
      return () => clearTimeout(timer);
    }
  }, [showTooltip]);

  const handleMainAction = () => {
    if (isMobile) {
      setIsExpanded(!isExpanded);
    } else {
      // On desktop, primary action is suggestions
      onSuggestionsToggle();
    }
  };

  const handleActionClick = (action, callback) => {
    callback();
    setIsExpanded(false);
    
    // Show brief tooltip feedback
    setShowTooltip(action);
  };

  const actions = [
    {
      id: 'suggestions',
      icon: showSuggestions ? <Lightbulb className="h-5 w-5" /> : <Sparkles className="h-5 w-5" />,
      label: showSuggestions ? 'Hide Suggestions' : 'Show Suggestions',
      onClick: () => handleActionClick('suggestions', onSuggestionsToggle),
      active: showSuggestions,
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      id: 'characters',
      icon: <Users className="h-5 w-5" />,
      label: 'Character Selector',
      onClick: () => handleActionClick('characters', onCharacterSelect),
      active: false,
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      id: 'immersion',
      icon: immersionEnabled ? <Eye className="h-5 w-5" /> : <EyeOff className="h-5 w-5" />,
      label: immersionEnabled ? 'Disable Immersion' : 'Enable Immersion',
      onClick: () => handleActionClick('immersion', onImmersionToggle),
      active: immersionEnabled,
      color: 'bg-purple-500 hover:bg-purple-600'
    },
    {
      id: 'sound',
      icon: soundEnabled ? <Volume2 className="h-5 w-5" /> : <VolumeX className="h-5 w-5" />,
      label: soundEnabled ? 'Mute Sounds' : 'Enable Sounds',
      onClick: () => handleActionClick('sound', onSoundToggle),
      active: soundEnabled,
      color: 'bg-orange-500 hover:bg-orange-600'
    }
  ];

  return (
    <div className={`fab-container fixed bottom-6 right-6 z-30 ${className}`}>
      {/* Tooltip */}
      {showTooltip && (
        <div className="absolute bottom-16 right-0 bg-background border border-border rounded-lg px-3 py-2 shadow-lg whitespace-nowrap text-sm animate-in fade-in-0 slide-in-from-bottom-2">
          {actions.find(a => a.id === showTooltip)?.label}
          <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-border" />
        </div>
      )}

      {/* Action Buttons (Mobile Expanded Menu) */}
      {isMobile && isExpanded && (
        <div className="absolute bottom-16 right-0 space-y-3">
          {actions.map((action, index) => (
            <div
              key={action.id}
              className="flex items-center gap-3 animate-in slide-in-from-bottom-2"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              {/* Label */}
              <div className="bg-background border border-border rounded-lg px-3 py-2 shadow-lg text-sm whitespace-nowrap">
                {action.label}
              </div>
              
              {/* Action Button */}
              <button
                onClick={action.onClick}
                className={`w-12 h-12 rounded-full ${action.color} text-white shadow-lg transition-all duration-200 hover:scale-110 active:scale-95 ${
                  action.active ? 'ring-2 ring-white ring-offset-2' : ''
                }`}
                aria-label={action.label}
              >
                {action.icon}
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Desktop Quick Actions */}
      {!isMobile && (
        <div className="absolute bottom-16 right-0 space-y-2 opacity-0 group-hover:opacity-100 transition-opacity">
          {actions.slice(1).map((action) => (
            <button
              key={action.id}
              onClick={action.onClick}
              onMouseEnter={() => setShowTooltip(action.id)}
              onMouseLeave={() => setShowTooltip(null)}
              className={`w-10 h-10 rounded-full ${action.color} text-white shadow-lg transition-all duration-200 hover:scale-110 ${
                action.active ? 'ring-2 ring-white ring-offset-2' : ''
              }`}
              aria-label={action.label}
            >
              {React.cloneElement(action.icon, { className: 'h-4 w-4' })}
            </button>
          ))}
        </div>
      )}

      {/* Main FAB */}
      <button
        onClick={handleMainAction}
        className={`group w-14 h-14 rounded-full bg-primary text-primary-foreground shadow-lg transition-all duration-200 hover:scale-110 active:scale-95 ${
          isExpanded ? 'rotate-45' : ''
        }`}
        aria-label={isMobile ? (isExpanded ? 'Close menu' : 'Open menu') : 'Toggle suggestions'}
        aria-expanded={isMobile ? isExpanded : undefined}
      >
        {isMobile ? (
          isExpanded ? <X className="h-6 w-6" /> : <Plus className="h-6 w-6" />
        ) : (
          showSuggestions ? <Lightbulb className="h-6 w-6" /> : <Sparkles className="h-6 w-6" />
        )}
      </button>

      {/* Backdrop for mobile */}
      {isMobile && isExpanded && (
        <div 
          className="fixed inset-0 bg-black/20 -z-10"
          onClick={() => setIsExpanded(false)}
          aria-hidden="true"
        />
      )}
    </div>
  );
};

export default FloatingActionButton;
