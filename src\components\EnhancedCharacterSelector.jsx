import React, { useState, useEffect } from 'react';
import { 
  User, 
  Crown, 
  Heart, 
  Zap, 
  Shield, 
  Sparkles,
  ChevronDown,
  Users,
  X,
  Check
} from 'lucide-react';

const EnhancedCharacterSelector = ({
  characters,
  activeCharacter,
  onCharacterSelect,
  storyArc,
  chatHistory,
  isMobile = false,
  isOpen = false,
  onClose,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCharacters, setFilteredCharacters] = useState(characters || []);
  const [characterSuggestions, setCharacterSuggestions] = useState([]);

  // Filter characters based on search
  useEffect(() => {
    if (!characters) return;
    
    const filtered = characters.filter(character =>
      character.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      character.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      character.type.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredCharacters(filtered);
  }, [characters, searchTerm]);

  // Generate character suggestions based on context
  useEffect(() => {
    if (characters && storyArc && chatHistory) {
      const suggestions = generateCharacterSuggestions(characters, storyArc, chatHistory);
      setCharacterSuggestions(suggestions);
    }
  }, [characters, storyArc, chatHistory]);

  const getCharacterIcon = (character) => {
    const type = character.type?.toLowerCase() || 'modern';
    const typeIcons = {
      fantasy: <Sparkles className="h-4 w-4" />,
      superhero: <Shield className="h-4 w-4" />,
      royal: <Crown className="h-4 w-4" />,
      romantic: <Heart className="h-4 w-4" />,
      powerful: <Zap className="h-4 w-4" />
    };
    return typeIcons[type] || <User className="h-4 w-4" />;
  };

  const getCharacterMoodColor = (character) => {
    const mood = character.mood?.toLowerCase() || 'neutral';
    const moodColors = {
      happy: 'border-yellow-400 bg-yellow-50 dark:bg-yellow-900/20',
      sad: 'border-blue-400 bg-blue-50 dark:bg-blue-900/20',
      angry: 'border-red-400 bg-red-50 dark:bg-red-900/20',
      determined: 'border-orange-400 bg-orange-50 dark:bg-orange-900/20',
      mysterious: 'border-purple-400 bg-purple-50 dark:bg-purple-900/20',
      romantic: 'border-pink-400 bg-pink-50 dark:bg-pink-900/20',
      confident: 'border-green-400 bg-green-50 dark:bg-green-900/20',
      neutral: 'border-gray-400 bg-gray-50 dark:bg-gray-900/20'
    };
    return moodColors[mood] || moodColors.neutral;
  };

  const getCharacterRelevance = (character) => {
    const suggestion = characterSuggestions.find(s => s.character.name === character.name);
    return suggestion?.relevance || 'normal';
  };

  const getSuggestionReason = (character) => {
    const suggestion = characterSuggestions.find(s => s.character.name === character.name);
    return suggestion?.reason || '';
  };

  const handleCharacterSelect = (character) => {
    onCharacterSelect(character);
    if (isMobile) {
      onClose();
    }
  };

  // Mobile bottom sheet style
  if (isMobile) {
    return (
      <>
        {/* Backdrop */}
        {isOpen && (
          <div 
            className="fixed inset-0 bg-black/50 z-40"
            onClick={onClose}
            aria-hidden="true"
          />
        )}
        
        {/* Bottom Sheet */}
        <div 
          className={`fixed bottom-0 left-0 right-0 bg-background border-t border-border rounded-t-xl z-50 transform transition-transform duration-300 ${
            isOpen ? 'translate-y-0' : 'translate-y-full'
          }`}
          role="dialog"
          aria-modal="true"
          aria-labelledby="character-selector-title"
        >
          {/* Handle */}
          <div className="flex justify-center pt-3 pb-2">
            <div className="w-8 h-1 bg-muted-foreground/30 rounded-full" />
          </div>
          
          {/* Header */}
          <div className="flex items-center justify-between px-4 pb-3 border-b border-border">
            <h2 id="character-selector-title" className="text-lg font-semibold">
              Choose Character
            </h2>
            <button
              onClick={onClose}
              className="p-2 rounded-full hover:bg-secondary/80 transition-colors"
              aria-label="Close character selector"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Search */}
          <div className="p-4 border-b border-border">
            <input
              type="text"
              placeholder="Search characters..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 rounded-lg border border-input bg-background text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              aria-label="Search characters"
            />
          </div>

          {/* Character List */}
          <div className="max-h-96 overflow-y-auto">
            {filteredCharacters.map((character) => {
              const relevance = getCharacterRelevance(character);
              const reason = getSuggestionReason(character);
              const isActive = activeCharacter?.name === character.name;
              
              return (
                <button
                  key={character.name}
                  onClick={() => handleCharacterSelect(character)}
                  className={`w-full text-left p-4 border-b border-border last:border-b-0 transition-colors ${
                    isActive
                      ? 'bg-primary/10 border-primary/20'
                      : 'hover:bg-secondary/50 active:bg-secondary/80'
                  }`}
                  aria-pressed={isActive}
                >
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-full ${getCharacterMoodColor(character)}`}>
                      {getCharacterIcon(character)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium truncate">
                          {character.name}
                        </span>
                        {isActive && <Check className="h-4 w-4 text-primary flex-shrink-0" />}
                        {relevance === 'high' && (
                          <span className="text-xs bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 px-2 py-0.5 rounded-full flex-shrink-0">
                            Recommended
                          </span>
                        )}
                      </div>
                      
                      <div className="text-sm text-muted-foreground mt-1 line-clamp-2">
                        {character.description}
                      </div>
                      
                      {reason && (
                        <div className="text-xs text-primary mt-1 italic">
                          💡 {reason}
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      </>
    );
  }

  // Desktop dropdown style
  return (
    <div className={`relative ${className}`}>
      {isOpen && (
        <div className="absolute bottom-full left-0 right-0 mb-2 bg-background border border-border rounded-lg shadow-lg z-20 max-h-64 overflow-hidden">
          {/* Search */}
          <div className="p-3 border-b border-border">
            <input
              type="text"
              placeholder="Search characters..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 rounded-lg border border-input bg-background text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              aria-label="Search characters"
            />
          </div>
          
          {/* Character List */}
          <div className="max-h-48 overflow-y-auto">
            {filteredCharacters.map((character) => {
              const relevance = getCharacterRelevance(character);
              const reason = getSuggestionReason(character);
              const isActive = activeCharacter?.name === character.name;
              
              return (
                <button
                  key={character.name}
                  onClick={() => handleCharacterSelect(character)}
                  className={`w-full text-left p-3 transition-colors ${
                    isActive
                      ? 'bg-primary/10'
                      : 'hover:bg-secondary/50'
                  }`}
                  aria-pressed={isActive}
                >
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-full ${getCharacterMoodColor(character)}`}>
                      {getCharacterIcon(character)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {character.name}
                        </span>
                        {isActive && <Check className="h-4 w-4 text-primary" />}
                        {relevance === 'high' && (
                          <span className="text-xs bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 px-2 py-0.5 rounded-full">
                            Recommended
                          </span>
                        )}
                      </div>
                      
                      <div className="text-xs text-muted-foreground mt-1 line-clamp-2">
                        {character.description}
                      </div>
                      
                      {reason && (
                        <div className="text-xs text-primary mt-1 italic">
                          💡 {reason}
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

// Helper function to generate character suggestions based on context
const generateCharacterSuggestions = (characters, storyArc, chatHistory) => {
  const suggestions = [];
  const recentMessages = chatHistory.slice(-5);
  const currentPhase = storyArc.currentPhase || 'introduction';
  const theme = storyArc.theme || 'general';

  characters.forEach(character => {
    let relevance = 'normal';
    let reason = '';

    // Check if character hasn't spoken recently
    const lastMessage = recentMessages.findLast(msg => msg.speaker === character.name);
    const messagesSinceLastSpoke = lastMessage 
      ? recentMessages.length - recentMessages.indexOf(lastMessage) - 1
      : recentMessages.length;

    if (messagesSinceLastSpoke >= 3) {
      relevance = 'suggested';
      reason = 'Haven\'t heard from them lately';
    }

    // Check if character type matches current theme
    if (character.type === theme) {
      relevance = 'high';
      reason = 'Perfect for current theme';
    }

    // Check if character mood matches story tension
    const tension = storyArc.currentTension || 'medium';
    if (
      (tension === 'high' && ['determined', 'angry', 'intense'].includes(character.mood)) ||
      (tension === 'low' && ['happy', 'calm', 'peaceful'].includes(character.mood))
    ) {
      relevance = 'suggested';
      reason = 'Mood fits the current tension';
    }

    suggestions.push({
      character,
      relevance,
      reason
    });
  });

  return suggestions;
};

export default EnhancedCharacterSelector;
