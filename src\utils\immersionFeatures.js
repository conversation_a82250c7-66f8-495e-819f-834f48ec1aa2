// Immersion features for enhanced chat experience

/**
 * Get ambient sound suggestions based on story setting and context
 * @param {Object} storyArc - Current story arc
 * @param {string} timeOfDay - Time context (day/night)
 * @param {Array} chatHistory - Recent messages for context
 * @returns {Object} Ambient sound configuration
 */
export const getAmbientSoundSuggestions = (storyArc, timeOfDay, chatHistory) => {
  const theme = storyArc?.theme || 'general';
  const currentPhase = storyArc?.currentPhase || 'introduction';
  const tension = storyArc?.currentTension || 'medium';

  // Analyze recent messages for context clues
  const hasAction = chatHistory.some(msg => msg.isAction);
  const hasConflict = chatHistory.some(msg => 
    msg.message && /fight|battle|attack|conflict/.test(msg.message.toLowerCase())
  );

  const soundConfig = {
    primary: null,
    secondary: null,
    intensity: getTensionIntensity(tension),
    timeVariant: timeOfDay
  };

  // Theme-based ambient sounds
  const themeSounds = {
    fantasy: {
      peaceful: ['mystical_forest', 'gentle_wind', 'distant_magic'],
      tense: ['ominous_drums', 'dark_magic', 'creature_sounds'],
      action: ['battle_horns', 'clashing_steel', 'magical_explosions']
    },
    superhero: {
      peaceful: ['city_ambience', 'gentle_breeze', 'distant_traffic'],
      tense: ['alarm_sirens', 'helicopter_sounds', 'urgent_radio'],
      action: ['explosion_sounds', 'energy_blasts', 'heroic_music']
    },
    scifi: {
      peaceful: ['space_ambience', 'computer_hums', 'gentle_tech'],
      tense: ['warning_beeps', 'system_alerts', 'mechanical_stress'],
      action: ['laser_fire', 'ship_engines', 'tech_explosions']
    },
    romance: {
      peaceful: ['soft_music', 'gentle_rain', 'fireplace_crackle'],
      tense: ['heartbeat', 'nervous_breathing', 'emotional_music'],
      action: ['passionate_music', 'rain_storm', 'dramatic_strings']
    },
    mystery: {
      peaceful: ['subtle_tension', 'clock_ticking', 'paper_rustling'],
      tense: ['suspenseful_music', 'footsteps', 'door_creaks'],
      action: ['chase_music', 'urgent_steps', 'dramatic_reveal']
    }
  };

  const sounds = themeSounds[theme] || themeSounds.fantasy;
  
  // Select appropriate sound category
  let category = 'peaceful';
  if (hasConflict || currentPhase === 'climax') {
    category = 'action';
  } else if (tension === 'high' || currentPhase === 'rising') {
    category = 'tense';
  }

  soundConfig.primary = sounds[category][0];
  soundConfig.secondary = sounds[category][1];

  // Time-based modifications
  if (timeOfDay === 'night') {
    soundConfig.nightVariants = ['night_sounds', 'owl_hoots', 'gentle_wind'];
  }

  return soundConfig;
};

/**
 * Get visual mood indicators based on story state
 * @param {Object} storyArc - Current story arc
 * @param {Object} activeCharacter - Current character
 * @param {Array} chatHistory - Recent messages
 * @returns {Object} Visual mood configuration
 */
export const getVisualMoodIndicators = (storyArc, activeCharacter, chatHistory) => {
  const tension = storyArc?.currentTension || 'medium';
  const theme = storyArc?.theme || 'general';
  const characterMood = activeCharacter?.mood || 'neutral';

  // Analyze recent emotional content
  const recentEmotions = analyzeEmotionalContent(chatHistory);

  const moodConfig = {
    primaryColor: getMoodColor(characterMood, tension),
    secondaryColor: getThemeColor(theme),
    intensity: getTensionIntensity(tension),
    animation: getMoodAnimation(recentEmotions),
    particles: getParticleEffects(theme, tension)
  };

  return moodConfig;
};

/**
 * Get dynamic background elements based on scene context
 * @param {Object} storyArc - Current story arc
 * @param {string} timeOfDay - Time context
 * @param {Array} chatHistory - Recent messages
 * @returns {Object} Background configuration
 */
export const getDynamicBackgroundElements = (storyArc, timeOfDay, chatHistory) => {
  const theme = storyArc?.theme || 'general';
  const currentPhase = storyArc?.currentPhase || 'introduction';
  const keyLocations = storyArc?.keyLocations || [];

  // Extract location context from recent messages
  const locationContext = extractLocationContext(chatHistory, keyLocations);

  const backgroundConfig = {
    primaryScene: getSceneBackground(theme, locationContext),
    timeOfDay: timeOfDay,
    weatherEffects: getWeatherEffects(chatHistory),
    atmosphericElements: getAtmosphericElements(theme, currentPhase),
    dynamicElements: getDynamicElements(currentPhase, storyArc.currentTension)
  };

  return backgroundConfig;
};

/**
 * Get contextual quick actions that adapt to narrative phase
 * @param {Object} storyArc - Current story arc
 * @param {Object} activeCharacter - Current character
 * @param {Array} chatHistory - Recent messages
 * @returns {Array} Adaptive quick actions
 */
export const getAdaptiveQuickActions = (storyArc, activeCharacter, chatHistory) => {
  const currentPhase = storyArc?.currentPhase || 'introduction';
  const tension = storyArc?.currentTension || 'medium';
  const theme = storyArc?.theme || 'general';

  const actions = [];

  // Phase-specific actions
  const phaseActions = {
    introduction: [
      { text: "Set the scene", icon: "🎭", category: "narrative" },
      { text: "Introduce yourself", icon: "👋", category: "social" },
      { text: "Ask a question", icon: "❓", category: "dialogue" }
    ],
    rising: [
      { text: "Raise the stakes", icon: "⚡", category: "tension" },
      { text: "Create conflict", icon: "⚔️", category: "conflict" },
      { text: "Reveal information", icon: "💡", category: "revelation" }
    ],
    climax: [
      { text: "Make crucial choice", icon: "🎯", category: "decisive" },
      { text: "Confront challenge", icon: "🛡️", category: "confrontation" },
      { text: "Use full power", icon: "💥", category: "climactic" }
    ],
    resolution: [
      { text: "Reflect on events", icon: "🤔", category: "reflection" },
      { text: "Plan next steps", icon: "🗺️", category: "planning" },
      { text: "Express gratitude", icon: "💝", category: "emotional" }
    ]
  };

  actions.push(...(phaseActions[currentPhase] || phaseActions.introduction));

  // Character-specific actions based on personality
  if (activeCharacter?.personality) {
    actions.push(...getPersonalityQuickActions(activeCharacter.personality));
  }

  // Theme-specific actions
  actions.push(...getThemeQuickActions(theme));

  return actions.slice(0, 6); // Return top 6 actions
};

// Helper functions

const getTensionIntensity = (tension) => {
  const intensityMap = {
    'low': 0.3,
    'medium': 0.6,
    'high': 0.9,
    'extreme': 1.0
  };
  return intensityMap[tension] || 0.6;
};

const getMoodColor = (mood, tension) => {
  const moodColors = {
    happy: '#FFD700',
    sad: '#4169E1',
    angry: '#DC143C',
    determined: '#FF4500',
    mysterious: '#8A2BE2',
    romantic: '#FF69B4',
    neutral: '#708090'
  };

  let baseColor = moodColors[mood] || moodColors.neutral;
  
  // Adjust intensity based on tension
  if (tension === 'high') {
    baseColor = adjustColorIntensity(baseColor, 1.3);
  } else if (tension === 'low') {
    baseColor = adjustColorIntensity(baseColor, 0.7);
  }

  return baseColor;
};

const getThemeColor = (theme) => {
  const themeColors = {
    fantasy: '#9370DB',
    superhero: '#1E90FF',
    scifi: '#00CED1',
    romance: '#FF1493',
    mystery: '#2F4F4F',
    general: '#696969'
  };
  return themeColors[theme] || themeColors.general;
};

const getMoodAnimation = (emotions) => {
  if (emotions.includes('excited') || emotions.includes('happy')) {
    return 'pulse';
  } else if (emotions.includes('sad') || emotions.includes('melancholy')) {
    return 'fade';
  } else if (emotions.includes('angry') || emotions.includes('intense')) {
    return 'shake';
  }
  return 'gentle-glow';
};

const getParticleEffects = (theme, tension) => {
  const effects = {
    fantasy: tension === 'high' ? 'magical-sparks' : 'floating-lights',
    superhero: tension === 'high' ? 'energy-bursts' : 'subtle-glow',
    scifi: tension === 'high' ? 'data-streams' : 'tech-particles',
    romance: 'heart-particles',
    mystery: 'shadow-wisps'
  };
  return effects[theme] || 'gentle-sparkles';
};

const analyzeEmotionalContent = (chatHistory) => {
  const emotions = [];
  const emotionKeywords = {
    happy: ['happy', 'joy', 'excited', 'cheerful', 'delighted'],
    sad: ['sad', 'sorrow', 'melancholy', 'depressed', 'grief'],
    angry: ['angry', 'rage', 'furious', 'mad', 'irritated'],
    excited: ['excited', 'thrilled', 'energetic', 'enthusiastic'],
    romantic: ['love', 'romantic', 'passion', 'affection', 'tender']
  };

  chatHistory.slice(-5).forEach(msg => {
    if (msg.message) {
      const text = msg.message.toLowerCase();
      Object.entries(emotionKeywords).forEach(([emotion, keywords]) => {
        if (keywords.some(keyword => text.includes(keyword))) {
          emotions.push(emotion);
        }
      });
    }
  });

  return [...new Set(emotions)]; // Remove duplicates
};

const extractLocationContext = (chatHistory, keyLocations) => {
  // Look for location mentions in recent messages
  const locationMentions = [];
  
  chatHistory.slice(-3).forEach(msg => {
    if (msg.message) {
      keyLocations.forEach(location => {
        if (msg.message.toLowerCase().includes(location.toLowerCase())) {
          locationMentions.push(location);
        }
      });
    }
  });

  return locationMentions[0] || 'general';
};

const getSceneBackground = (theme, locationContext) => {
  const backgrounds = {
    fantasy: {
      general: 'mystical-forest',
      castle: 'ancient-castle',
      forest: 'enchanted-woods',
      mountain: 'misty-peaks'
    },
    superhero: {
      general: 'city-skyline',
      city: 'urban-landscape',
      tower: 'skyscraper-view',
      street: 'city-street'
    },
    scifi: {
      general: 'space-station',
      ship: 'starship-bridge',
      planet: 'alien-world',
      lab: 'tech-laboratory'
    }
  };

  const themeBackgrounds = backgrounds[theme] || backgrounds.fantasy;
  return themeBackgrounds[locationContext] || themeBackgrounds.general;
};

const getWeatherEffects = (chatHistory) => {
  const weatherKeywords = {
    rain: ['rain', 'storm', 'thunder', 'lightning'],
    snow: ['snow', 'blizzard', 'cold', 'winter'],
    wind: ['wind', 'breeze', 'gust', 'windy'],
    clear: ['sunny', 'clear', 'bright', 'sunshine']
  };

  for (const msg of chatHistory.slice(-3)) {
    if (msg.message) {
      const text = msg.message.toLowerCase();
      for (const [weather, keywords] of Object.entries(weatherKeywords)) {
        if (keywords.some(keyword => text.includes(keyword))) {
          return weather;
        }
      }
    }
  }

  return 'clear';
};

const getAtmosphericElements = (theme, phase) => {
  const elements = {
    fantasy: ['floating-runes', 'magical-mist', 'ethereal-lights'],
    superhero: ['city-lights', 'energy-fields', 'heroic-aura'],
    scifi: ['holographic-displays', 'energy-streams', 'tech-interfaces'],
    romance: ['soft-lighting', 'romantic-glow', 'gentle-sparkles'],
    mystery: ['shadows', 'fog', 'mysterious-silhouettes']
  };

  return elements[theme] || elements.fantasy;
};

const getDynamicElements = (phase, tension) => {
  if (phase === 'climax' && tension === 'high') {
    return ['intense-effects', 'dramatic-lighting', 'high-energy'];
  } else if (tension === 'high') {
    return ['tension-effects', 'dramatic-shadows'];
  } else if (phase === 'resolution') {
    return ['peaceful-effects', 'gentle-glow'];
  }
  return ['subtle-movement', 'ambient-glow'];
};

const getPersonalityQuickActions = (personality) => {
  const actions = [];
  
  if (personality.analytical > 7) {
    actions.push({ text: "Analyze situation", icon: "🔍", category: "analytical" });
  }
  if (personality.humor > 7) {
    actions.push({ text: "Make a joke", icon: "😄", category: "humorous" });
  }
  if (personality.emotional > 7) {
    actions.push({ text: "Express feelings", icon: "💭", category: "emotional" });
  }

  return actions;
};

const getThemeQuickActions = (theme) => {
  const themeActions = {
    fantasy: [
      { text: "Cast spell", icon: "✨", category: "magic" },
      { text: "Sense magic", icon: "🔮", category: "mystical" }
    ],
    superhero: [
      { text: "Use power", icon: "⚡", category: "heroic" },
      { text: "Protect others", icon: "🛡️", category: "protective" }
    ],
    scifi: [
      { text: "Scan area", icon: "📡", category: "tech" },
      { text: "Access data", icon: "💻", category: "information" }
    ]
  };

  return themeActions[theme] || [];
};

const adjustColorIntensity = (color, factor) => {
  // Simple color intensity adjustment (would need proper color manipulation in real implementation)
  return color;
};
