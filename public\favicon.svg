<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <rect width="512" height="512" rx="128" fill="url(#paint0_linear)" />

  <!-- V letter - stylized -->
  <path d="M160 128L256 384L352 128" stroke="white" stroke-width="48" stroke-linecap="round" stroke-linejoin="round" />

  <!-- Sparkle effect -->
  <circle cx="256" cy="256" r="180" stroke="white" stroke-width="4" stroke-opacity="0.2" />
  <circle cx="256" cy="256" r="200" stroke="white" stroke-width="2" stroke-opacity="0.1" />

  <!-- Define the gradient -->
  <defs>
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="512" y2="512" gradientUnits="userSpaceOnUse">
      <stop stop-color="#1E293B" />
      <stop offset="1" stop-color="#9333EA" />
    </linearGradient>
  </defs>

  <!-- Add media query for dark mode support -->
  <style>
    @media (prefers-color-scheme: dark) {
      rect { fill: url(#paint0_linear); }
    }
    @media (prefers-color-scheme: light) {
      rect { fill: url(#paint0_linear); }
    }
  </style>
</svg>
