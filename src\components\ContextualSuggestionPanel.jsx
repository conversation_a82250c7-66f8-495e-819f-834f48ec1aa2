import React, { useState, useEffect } from 'react';
import { 
  Lightbulb, 
  <PERSON>rkles, 
  Users, 
  MapPin, 
  Clock, 
  Zap,
  ChevronDown,
  ChevronUp,
  X,
  Wand2,
  MessageSquare,
  Target
} from 'lucide-react';
import { generateContextAwareSuggestions, generateCharacterSpecificActions } from '../utils/intelligentSuggestions';

const ContextualSuggestionPanel = ({
  storyArc,
  chatHistory,
  activeCharacter,
  onSuggestionSelect,
  isMobile = false,
  isOpen = false,
  onClose,
  mode = 'contextual', // 'contextual' | 'character' | 'story'
  className = ''
}) => {
  const [suggestions, setSuggestions] = useState([]);
  const [characterActions, setCharacterActions] = useState([]);
  const [activeTab, setActiveTab] = useState(mode);
  const [isExpanded, setIsExpanded] = useState(!isMobile);

  // Get current time context
  const timeOfDay = new Date().getHours() >= 18 || new Date().getHours() <= 6 ? 'night' : 'day';

  // Update suggestions when context changes
  useEffect(() => {
    if (storyArc && activeCharacter) {
      const newSuggestions = generateContextAwareSuggestions(
        storyArc, 
        chatHistory, 
        activeCharacter, 
        timeOfDay
      );
      setSuggestions(newSuggestions);

      const newActions = generateCharacterSpecificActions(
        activeCharacter,
        storyArc,
        chatHistory
      );
      setCharacterActions(newActions);
    }
  }, [storyArc, chatHistory, activeCharacter, timeOfDay]);

  // Set active tab based on mode prop
  useEffect(() => {
    setActiveTab(mode);
  }, [mode]);

  const handleSuggestionClick = (suggestion) => {
    if (onSuggestionSelect) {
      onSuggestionSelect(suggestion);
    }
    if (isMobile) {
      onClose();
    }
  };

  const getCategoryIcon = (category) => {
    const icons = {
      narrative: <Lightbulb className="h-3 w-3" />,
      character: <Users className="h-3 w-3" />,
      environment: <MapPin className="h-3 w-3" />,
      tension: <Zap className="h-3 w-3" />,
      emotional: <Sparkles className="h-3 w-3" />,
      action: <Target className="h-3 w-3" />,
      dialogue: <MessageSquare className="h-3 w-3" />
    };
    return icons[category] || <Lightbulb className="h-3 w-3" />;
  };

  const getCategoryColor = (category) => {
    const colors = {
      narrative: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
      character: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
      environment: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300',
      tension: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
      emotional: 'bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-300',
      action: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300',
      dialogue: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300'
    };
    return colors[category] || colors.narrative;
  };

  const getTabIcon = (tab) => {
    const icons = {
      contextual: <Wand2 className="h-4 w-4" />,
      character: <Users className="h-4 w-4" />,
      story: <Lightbulb className="h-4 w-4" />
    };
    return icons[tab] || <Wand2 className="h-4 w-4" />;
  };

  const getTabLabel = (tab) => {
    const labels = {
      contextual: 'Smart Ideas',
      character: `${activeCharacter?.name || 'Character'} Actions`,
      story: 'Story Flow'
    };
    return labels[tab] || 'Suggestions';
  };

  if (!storyArc || !activeCharacter) {
    return null;
  }

  // Mobile bottom sheet style
  if (isMobile) {
    return (
      <>
        {/* Backdrop */}
        {isOpen && (
          <div 
            className="fixed inset-0 bg-black/50 z-40"
            onClick={onClose}
            aria-hidden="true"
          />
        )}
        
        {/* Bottom Sheet */}
        <div 
          className={`fixed bottom-0 left-0 right-0 bg-background border-t border-border rounded-t-xl z-50 transform transition-transform duration-300 ${
            isOpen ? 'translate-y-0' : 'translate-y-full'
          }`}
          role="dialog"
          aria-modal="true"
          aria-labelledby="suggestions-title"
        >
          {/* Handle */}
          <div className="flex justify-center pt-3 pb-2">
            <div className="w-8 h-1 bg-muted-foreground/30 rounded-full" />
          </div>
          
          {/* Header */}
          <div className="flex items-center justify-between px-4 pb-3 border-b border-border">
            <div className="flex items-center gap-2">
              {getTabIcon(activeTab)}
              <h2 id="suggestions-title" className="text-lg font-semibold">
                {getTabLabel(activeTab)}
              </h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-full hover:bg-secondary/80 transition-colors"
              aria-label="Close suggestions"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Tab Navigation */}
          <div className="flex gap-1 p-4 border-b border-border">
            {['contextual', 'character', 'story'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`flex items-center gap-2 px-3 py-2 text-sm rounded-full transition-colors ${
                  activeTab === tab
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-secondary hover:bg-secondary/80'
                }`}
                aria-pressed={activeTab === tab}
              >
                {getTabIcon(tab)}
                <span className="hidden sm:inline">{getTabLabel(tab)}</span>
              </button>
            ))}
          </div>

          {/* Content */}
          <div className="max-h-96 overflow-y-auto p-4">
            {activeTab === 'contextual' && (
              <div className="space-y-3">
                <div className="text-xs text-muted-foreground mb-3">
                  Based on current scene and narrative flow
                </div>
                {suggestions.map((suggestion, index) => (
                  <SuggestionCard
                    key={index}
                    suggestion={suggestion}
                    onClick={handleSuggestionClick}
                    getCategoryIcon={getCategoryIcon}
                    getCategoryColor={getCategoryColor}
                  />
                ))}
              </div>
            )}

            {activeTab === 'character' && (
              <div className="space-y-3">
                <div className="text-xs text-muted-foreground mb-3">
                  Actions that fit {activeCharacter.name}'s personality
                </div>
                {characterActions.map((action, index) => (
                  <SuggestionCard
                    key={index}
                    suggestion={action}
                    onClick={handleSuggestionClick}
                    getCategoryIcon={getCategoryIcon}
                    getCategoryColor={getCategoryColor}
                  />
                ))}
              </div>
            )}

            {activeTab === 'story' && (
              <div className="space-y-3">
                <div className="text-xs text-muted-foreground mb-3">
                  Story progression suggestions
                </div>
                {suggestions.filter(s => s.category === 'narrative').map((suggestion, index) => (
                  <SuggestionCard
                    key={index}
                    suggestion={suggestion}
                    onClick={handleSuggestionClick}
                    getCategoryIcon={getCategoryIcon}
                    getCategoryColor={getCategoryColor}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Context Info */}
          <div className="px-4 py-3 border-t border-border bg-secondary/20">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center gap-2">
                <span>Phase: {storyArc.currentPhase}</span>
                <span>•</span>
                <span>Tension: {storyArc.currentTension}</span>
                <span>•</span>
                <span className="capitalize">{timeOfDay}</span>
              </div>
              <div className="flex items-center gap-1">
                <Zap className="h-3 w-3" />
                <span>AI-powered</span>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Desktop floating panel style
  return (
    <div className={`bg-background border border-border rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-border">
        <div className="flex items-center gap-2">
          {getTabIcon(activeTab)}
          <span className="text-sm font-medium">{getTabLabel(activeTab)}</span>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            <span className="capitalize">{timeOfDay}</span>
          </div>
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="p-1 rounded-full hover:bg-secondary/50 transition-colors"
          aria-label={isExpanded ? 'Collapse suggestions' : 'Expand suggestions'}
        >
          {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </button>
      </div>

      {isExpanded && (
        <div className="p-3">
          {/* Tab Navigation */}
          <div className="flex gap-1 mb-3">
            {['contextual', 'character', 'story'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`flex items-center gap-1 px-2 py-1 text-xs rounded-full transition-colors ${
                  activeTab === tab
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-secondary hover:bg-secondary/80'
                }`}
                aria-pressed={activeTab === tab}
              >
                {getTabIcon(tab)}
                <span>{tab === 'contextual' ? 'Smart' : tab === 'character' ? 'Actions' : 'Story'}</span>
              </button>
            ))}
          </div>

          {/* Content */}
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {activeTab === 'contextual' && suggestions.map((suggestion, index) => (
              <SuggestionCard
                key={index}
                suggestion={suggestion}
                onClick={handleSuggestionClick}
                getCategoryIcon={getCategoryIcon}
                getCategoryColor={getCategoryColor}
                compact
              />
            ))}

            {activeTab === 'character' && characterActions.map((action, index) => (
              <SuggestionCard
                key={index}
                suggestion={action}
                onClick={handleSuggestionClick}
                getCategoryIcon={getCategoryIcon}
                getCategoryColor={getCategoryColor}
                compact
              />
            ))}

            {activeTab === 'story' && suggestions.filter(s => s.category === 'narrative').map((suggestion, index) => (
              <SuggestionCard
                key={index}
                suggestion={suggestion}
                onClick={handleSuggestionClick}
                getCategoryIcon={getCategoryIcon}
                getCategoryColor={getCategoryColor}
                compact
              />
            ))}
          </div>

          {/* Context Info */}
          <div className="mt-3 pt-3 border-t border-border">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center gap-2">
                <span>Phase: {storyArc.currentPhase}</span>
                <span>•</span>
                <span>Tension: {storyArc.currentTension}</span>
              </div>
              <div className="flex items-center gap-1">
                <Zap className="h-3 w-3" />
                <span>AI</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Reusable suggestion card component
const SuggestionCard = ({ suggestion, onClick, getCategoryIcon, getCategoryColor, compact = false }) => (
  <button
    onClick={() => onClick(suggestion)}
    className={`w-full text-left ${compact ? 'p-2' : 'p-3'} rounded-lg border border-border hover:bg-secondary/50 transition-colors group`}
  >
    <div className="flex items-start gap-2">
      <div className={`p-1 rounded-full ${getCategoryColor(suggestion.category)}`}>
        {getCategoryIcon(suggestion.category)}
      </div>
      <div className="flex-1 min-w-0">
        <div className={`${compact ? 'text-sm' : 'text-sm'} font-medium group-hover:text-primary transition-colors line-clamp-2`}>
          {suggestion.text}
        </div>
        <div className="flex items-center gap-2 mt-1">
          <span className={`text-xs px-2 py-0.5 rounded-full ${getCategoryColor(suggestion.category)}`}>
            {suggestion.type}
          </span>
          <span className="text-xs text-muted-foreground">
            {suggestion.category}
          </span>
        </div>
      </div>
    </div>
  </button>
);

export default ContextualSuggestionPanel;
