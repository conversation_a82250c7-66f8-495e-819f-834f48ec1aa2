import React, { useState, useEffect } from 'react';
import { 
  Volume2, 
  VolumeX, 
  Eye, 
  Palette, 
  <PERSON>s,
  Sun,
  Moon,
  Cloud,
  Zap
} from 'lucide-react';
import { 
  getAmbientSoundSuggestions, 
  getVisualMoodIndicators, 
  getDynamicBackgroundElements,
  getAdaptiveQuickActions
} from '../utils/immersionFeatures';

const ImmersionFeatures = ({
  storyArc,
  activeCharacter,
  chatHistory,
  className = ''
}) => {
  const [soundEnabled, setSoundEnabled] = useState(false);
  const [visualEffectsEnabled, setVisualEffectsEnabled] = useState(true);
  const [currentMood, setCurrentMood] = useState(null);
  const [backgroundElements, setBackgroundElements] = useState(null);
  const [adaptiveActions, setAdaptiveActions] = useState([]);

  // Get current time context
  const timeOfDay = new Date().getHours() >= 18 || new Date().getHours() <= 6 ? 'night' : 'day';

  // Update immersion features when context changes
  useEffect(() => {
    if (storyArc && activeCharacter) {
      // Update ambient sound suggestions
      const soundConfig = getAmbientSoundSuggestions(storyArc, timeOfDay, chatHistory);
      
      // Update visual mood indicators
      const moodConfig = getVisualMoodIndicators(storyArc, activeCharacter, chatHistory);
      setCurrentMood(moodConfig);
      
      // Update background elements
      const bgConfig = getDynamicBackgroundElements(storyArc, timeOfDay, chatHistory);
      setBackgroundElements(bgConfig);
      
      // Update adaptive quick actions
      const actions = getAdaptiveQuickActions(storyArc, activeCharacter, chatHistory);
      setAdaptiveActions(actions);
    }
  }, [storyArc, activeCharacter, chatHistory, timeOfDay]);

  const getTimeIcon = () => {
    return timeOfDay === 'night' ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />;
  };

  const getMoodGradient = () => {
    if (!currentMood) return 'from-gray-500/20 to-gray-600/20';
    
    const { primaryColor, secondaryColor, intensity } = currentMood;
    const opacity = Math.max(0.1, intensity * 0.3);
    
    return `from-[${primaryColor}]/${opacity} to-[${secondaryColor}]/${opacity}`;
  };

  const getWeatherIcon = (weather) => {
    const icons = {
      rain: <Cloud className="h-4 w-4" />,
      clear: <Sun className="h-4 w-4" />,
      storm: <Zap className="h-4 w-4" />,
      wind: <Waves className="h-4 w-4" />
    };
    return icons[weather] || <Sun className="h-4 w-4" />;
  };

  if (!storyArc || !activeCharacter) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      {/* Ambient Background Overlay */}
      {visualEffectsEnabled && currentMood && (
        <div 
          className={`absolute inset-0 bg-gradient-to-br ${getMoodGradient()} pointer-events-none transition-all duration-1000`}
          style={{
            animation: currentMood.animation === 'pulse' ? 'pulse 2s infinite' : 
                      currentMood.animation === 'fade' ? 'fade 3s infinite' :
                      currentMood.animation === 'shake' ? 'shake 0.5s infinite' : 'none'
          }}
        />
      )}

      {/* Immersion Control Panel */}
      <div className="absolute top-4 right-4 bg-background/80 backdrop-blur-sm border border-border rounded-lg p-3 shadow-lg z-10">
        <div className="flex items-center gap-3">
          {/* Time of Day Indicator */}
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            {getTimeIcon()}
            <span className="capitalize">{timeOfDay}</span>
          </div>

          {/* Weather Indicator */}
          {backgroundElements?.weatherEffects && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              {getWeatherIcon(backgroundElements.weatherEffects)}
              <span className="capitalize">{backgroundElements.weatherEffects}</span>
            </div>
          )}

          {/* Sound Toggle */}
          <button
            onClick={() => setSoundEnabled(!soundEnabled)}
            className={`p-1 rounded-full transition-colors ${
              soundEnabled 
                ? 'bg-primary text-primary-foreground' 
                : 'bg-secondary hover:bg-secondary/80'
            }`}
            title={soundEnabled ? 'Disable ambient sounds' : 'Enable ambient sounds'}
          >
            {soundEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
          </button>

          {/* Visual Effects Toggle */}
          <button
            onClick={() => setVisualEffectsEnabled(!visualEffectsEnabled)}
            className={`p-1 rounded-full transition-colors ${
              visualEffectsEnabled 
                ? 'bg-primary text-primary-foreground' 
                : 'bg-secondary hover:bg-secondary/80'
            }`}
            title={visualEffectsEnabled ? 'Disable visual effects' : 'Enable visual effects'}
          >
            {visualEffectsEnabled ? <Eye className="h-4 w-4" /> : <Palette className="h-4 w-4" />}
          </button>
        </div>

        {/* Mood Indicator */}
        {currentMood && (
          <div className="mt-2 pt-2 border-t border-border">
            <div className="flex items-center gap-2 text-xs">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: currentMood.primaryColor }}
              />
              <span className="text-muted-foreground">
                Mood: {activeCharacter.mood || 'neutral'}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Adaptive Quick Actions */}
      {adaptiveActions.length > 0 && (
        <div className="absolute bottom-4 left-4 right-4 z-10">
          <div className="bg-background/90 backdrop-blur-sm border border-border rounded-lg p-3 shadow-lg">
            <div className="text-xs text-muted-foreground mb-2 flex items-center gap-1">
              <Zap className="h-3 w-3" />
              Contextual Actions
            </div>
            <div className="flex gap-2 flex-wrap">
              {adaptiveActions.map((action, index) => (
                <button
                  key={index}
                  className="flex items-center gap-1 px-3 py-1.5 bg-secondary hover:bg-secondary/80 rounded-full text-xs transition-colors"
                  title={`${action.category} action`}
                >
                  <span>{action.icon}</span>
                  <span>{action.text}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Particle Effects */}
      {visualEffectsEnabled && currentMood?.particles && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className={`particles-${currentMood.particles}`}>
            {/* Particle elements would be rendered here */}
            {Array.from({ length: 20 }).map((_, i) => (
              <div
                key={i}
                className="absolute w-1 h-1 bg-current opacity-30 rounded-full animate-float"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 3}s`,
                  animationDuration: `${3 + Math.random() * 2}s`
                }}
              />
            ))}
          </div>
        </div>
      )}

      {/* Story Arc Progress Indicator */}
      <div className="absolute top-4 left-4 bg-background/80 backdrop-blur-sm border border-border rounded-lg p-2 shadow-lg z-10">
        <div className="text-xs text-muted-foreground mb-1">Story Progress</div>
        <div className="flex items-center gap-2">
          <div className="w-16 h-1 bg-secondary rounded-full overflow-hidden">
            <div 
              className="h-full bg-primary transition-all duration-500"
              style={{ 
                width: `${getPhaseProgress(storyArc.currentPhase)}%` 
              }}
            />
          </div>
          <span className="text-xs capitalize">{storyArc.currentPhase}</span>
        </div>
        <div className="text-xs text-muted-foreground mt-1">
          Tension: {storyArc.currentTension}
        </div>
      </div>

      {/* CSS for animations */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-10px) rotate(180deg); }
        }
        
        @keyframes fade {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 0.1; }
        }
        
        @keyframes shake {
          0%, 100% { transform: translateX(0); }
          25% { transform: translateX(-2px); }
          75% { transform: translateX(2px); }
        }
        
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

// Helper function to calculate phase progress
const getPhaseProgress = (phase) => {
  const phaseMap = {
    introduction: 25,
    rising: 50,
    climax: 75,
    resolution: 100
  };
  return phaseMap[phase] || 0;
};

export default ImmersionFeatures;
