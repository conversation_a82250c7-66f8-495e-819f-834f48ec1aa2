import React, { useState, useEffect } from 'react';
import { 
  <PERSON>bulb, 
  <PERSON>rk<PERSON>, 
  Users, 
  MapPin, 
  Clock, 
  Zap,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { generateContextAwareSuggestions, generateCharacterSpecificActions } from '../utils/intelligentSuggestions';

const IntelligentSuggestionPanel = ({
  storyArc,
  chatHistory,
  activeCharacter,
  onSuggestionSelect,
  className = ''
}) => {
  const [suggestions, setSuggestions] = useState([]);
  const [characterActions, setCharacterActions] = useState([]);
  const [isExpanded, setIsExpanded] = useState(true);
  const [activeTab, setActiveTab] = useState('story');

  // Get current time context
  const timeOfDay = new Date().getHours() >= 18 || new Date().getHours() <= 6 ? 'night' : 'day';

  // Update suggestions when context changes
  useEffect(() => {
    if (storyArc && activeCharacter) {
      const newSuggestions = generateContextAwareSuggestions(
        storyArc, 
        chatHistory, 
        activeCharacter, 
        timeOfDay
      );
      setSuggestions(newSuggestions);

      const newActions = generateCharacterSpecificActions(
        activeCharacter,
        storyArc,
        chatHistory
      );
      setCharacterActions(newActions);
    }
  }, [storyArc, chatHistory, activeCharacter, timeOfDay]);

  const handleSuggestionClick = (suggestion) => {
    if (onSuggestionSelect) {
      onSuggestionSelect(suggestion);
    }
  };

  const getCategoryIcon = (category) => {
    const icons = {
      narrative: <Lightbulb className="h-3 w-3" />,
      character: <Users className="h-3 w-3" />,
      environment: <MapPin className="h-3 w-3" />,
      tension: <Zap className="h-3 w-3" />,
      emotional: <Sparkles className="h-3 w-3" />,
      action: <Zap className="h-3 w-3" />,
      dialogue: <Users className="h-3 w-3" />
    };
    return icons[category] || <Lightbulb className="h-3 w-3" />;
  };

  const getCategoryColor = (category) => {
    const colors = {
      narrative: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
      character: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
      environment: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300',
      tension: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
      emotional: 'bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-300',
      action: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300',
      dialogue: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300'
    };
    return colors[category] || colors.narrative;
  };

  if (!storyArc || !activeCharacter) {
    return null;
  }

  return (
    <div className={`bg-background border border-border rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-border">
        <div className="flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-primary" />
          <span className="text-sm font-medium">Smart Suggestions</span>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            <span>{timeOfDay}</span>
          </div>
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="p-1 rounded-full hover:bg-secondary/50 transition-colors"
        >
          {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </button>
      </div>

      {isExpanded && (
        <div className="p-3">
          {/* Tab Navigation */}
          <div className="flex gap-1 mb-3">
            <button
              onClick={() => setActiveTab('story')}
              className={`px-3 py-1 text-xs rounded-full transition-colors ${
                activeTab === 'story'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-secondary hover:bg-secondary/80'
              }`}
            >
              Story Ideas
            </button>
            <button
              onClick={() => setActiveTab('character')}
              className={`px-3 py-1 text-xs rounded-full transition-colors ${
                activeTab === 'character'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-secondary hover:bg-secondary/80'
              }`}
            >
              {activeCharacter.name} Actions
            </button>
          </div>

          {/* Story Suggestions Tab */}
          {activeTab === 'story' && (
            <div className="space-y-2">
              <div className="text-xs text-muted-foreground mb-2">
                Based on current scene and narrative flow
              </div>
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="w-full text-left p-2 rounded-lg border border-border hover:bg-secondary/50 transition-colors group"
                >
                  <div className="flex items-start gap-2">
                    <div className={`p-1 rounded-full ${getCategoryColor(suggestion.category)}`}>
                      {getCategoryIcon(suggestion.category)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium group-hover:text-primary transition-colors">
                        {suggestion.text}
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <span className={`text-xs px-2 py-0.5 rounded-full ${getCategoryColor(suggestion.category)}`}>
                          {suggestion.type}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {suggestion.category}
                        </span>
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}

          {/* Character Actions Tab */}
          {activeTab === 'character' && (
            <div className="space-y-2">
              <div className="text-xs text-muted-foreground mb-2">
                Actions that fit {activeCharacter.name}'s personality
              </div>
              {characterActions.map((action, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(action)}
                  className="w-full text-left p-2 rounded-lg border border-border hover:bg-secondary/50 transition-colors group"
                >
                  <div className="flex items-start gap-2">
                    <div className={`p-1 rounded-full ${getCategoryColor(action.category)}`}>
                      {getCategoryIcon(action.category)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium group-hover:text-primary transition-colors">
                        {action.text}
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <span className={`text-xs px-2 py-0.5 rounded-full ${getCategoryColor(action.category)}`}>
                          {action.type}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {action.category}
                        </span>
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}

          {/* Context Info */}
          <div className="mt-3 pt-3 border-t border-border">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center gap-2">
                <span>Phase: {storyArc.currentPhase}</span>
                <span>•</span>
                <span>Tension: {storyArc.currentTension}</span>
              </div>
              <div className="flex items-center gap-1">
                <Zap className="h-3 w-3" />
                <span>AI-powered</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IntelligentSuggestionPanel;
